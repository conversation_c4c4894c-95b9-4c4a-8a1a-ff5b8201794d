import React, { useState, useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
  Alert,
} from "react-native";
import { useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  FadeInUp,
} from 'react-native-reanimated';
import { useAuthStore } from "../store/authStore";
import { useAIStore } from "../store/aiStore";
import { UserPreferences } from "../services/authService";
import { PreferencesOnboarding } from "../components/PreferencesOnboarding";
import { SmartPreferencesWizard } from "../components/SmartPreferencesWizard";
import { LogService } from "../services/logService";

// Define theme colors to match dashboard
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Define specific error types for better error handling
class PreferencesSaveError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'PreferencesSaveError';
  }
}

class NavigationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NavigationError';
  }
}

class PropertyMatchingError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'PropertyMatchingError';
  }
}

// Enhanced Header Component to match dashboard
const Header = ({
  showBackButton = false,
  onBack,
}: {
  showBackButton?: boolean;
  onBack?: () => void;
}) => {
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        {showBackButton ? (
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <View style={styles.backButtonContainer}>
              <Ionicons name="arrow-back" size={24} color={THEME.light} />
            </View>
          </TouchableOpacity>
        ) : (
          <View style={styles.placeholder} />
        )}

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Preferences</Text>
            <Text style={styles.headerSubtitle}>Customize your experience</Text>
          </View>
        </View>

        <View style={styles.placeholder} />
      </Animated.View>
    </LinearGradient>
  );
};

export default function PreferencesSetupScreen() {
  const router = useRouter();
  const { user, updatePreferences, isLoading } = useAuthStore();
  const { requestPropertyMatching } = useAIStore();
  const [isSaving, setIsSaving] = useState(false);

  const [savingStage, setSavingStage] = useState<'idle' | 'saving' | 'matching' | 'navigating'>('idle');
  const [saveAttemptCount, setSaveAttemptCount] = useState(0);

  // Log component mount and store navigation data
  useEffect(() => {
    const logNavigation = async () => {
      // Track that we navigated to preferences
      const timestamp = new Date().getTime();
      const navigationEvent = {
        screen: 'preferences',
        timestamp,
        hasPreferences: !!(user?.preferences)
      };

      // Store this navigation event with AsyncStorage
      try {
        await AsyncStorage.setItem('lastNavigation', JSON.stringify(navigationEvent));
      } catch (storageError) {
        // Only log critical errors
        LogService.error('PreferencesScreen', 'Failed to store navigation data', storageError);
      }
    };

    logNavigation();
  }, []);

  // Handle save preferences
  const handleSavePreferences = async (preferences: UserPreferences) => {
    // Prevent multiple submission attempts
    if (isSaving) {
      return false;
    }

    // Track save attempts to prevent excessive retries
    setSaveAttemptCount(prev => prev + 1);
    if (saveAttemptCount > 3) {
      Alert.alert(
        "Too Many Attempts",
        "We've detected multiple save attempts. Please try again later.",
        [{ text: "OK" }]
      );
      return false;
    }

    try {
      // Validate input parameters
      if (!preferences) {
        throw new PreferencesSaveError('No preferences data provided');
      }

      if (!user) {
        throw new PreferencesSaveError('Cannot save preferences: User not authenticated');
      }

      // Set loading state
      setIsSaving(true);
      setSavingStage('saving');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Log the preferences being saved
      console.log('Saving preferences:', JSON.stringify(preferences, null, 2));

      // Ensure all required fields are present
      const completePreferences: UserPreferences = {
        minPrice: preferences.minPrice || 0,
        maxPrice: preferences.maxPrice || 2000,
        preferredLocations: preferences.preferredLocations || [],
        propertyTypes: preferences.propertyTypes || ['apartment'],
        minRooms: preferences.minRooms || 1,
        maxRooms: preferences.maxRooms || 3,
        amenities: preferences.amenities || [],
        notifications: preferences.notifications || {
          email: true,
          push: true,
          sms: false,
        },
      };

      // Update preferences in backend first
      const success = await updatePreferences(completePreferences);

      if (!success) {
        throw new PreferencesSaveError('Failed to update preferences in backend');
      }

      // Store navigation attempt to track potential loops
      try {
        // Track that we're navigating from preferences to dashboard
        const timestamp = new Date().getTime();
        const navigationEvent = {
          from: 'preferences',
          to: 'dashboard',
          timestamp,
          hasPreferences: true, // We just saved them successfully
          navigationId: `nav_${timestamp}`
        };

        // Store this navigation event with AsyncStorage
        await AsyncStorage.setItem('lastNavigation', JSON.stringify(navigationEvent));


      } catch (error) {
        // Non-critical error, just continue
      }

      // After preferences are successfully saved to backend, trigger AI matching
      try {
        // Update loading state to show we're matching properties
        setSavingStage('matching');

        // Get the updated user data with the latest preferences from the backend
        const updatedUser = { ...user, preferences };

        await requestPropertyMatching(preferences, updatedUser);
      } catch (error) {
        // Handle property matching error but continue with navigation
        Alert.alert(
          "Property Matching Issue",
          "Your preferences were saved successfully, but we couldn't find matching properties at this time. You can try again later.",
          [{ text: "OK" }]
        );
      } finally {
        // Update loading state to show we're navigating
        setSavingStage('navigating');

        // Use an immediately invoked async function to handle the async operations in the finally block
        (async () => {
          try {
            // Store navigation data in AsyncStorage to prevent loops
            const now = new Date().getTime();
            const navigationEvent = {
              from: 'preferences',
              to: 'dashboard',
              timestamp: now,
              hasPreferences: true, // Flag that preferences were successfully saved
              navigationId: `nav_${now}`
            };

            // Store this navigation event with AsyncStorage
            await AsyncStorage.setItem('lastNavigation', JSON.stringify(navigationEvent));

            // Force a refresh of the user data before navigating
            try {
              // Get the current user data from the backend to ensure it's up to date
              await updatePreferences(preferences);
            } catch (refreshError) {
              console.error('Failed to refresh user data:', refreshError);
              // Continue with navigation even if refresh fails
            }

            // Add a small delay to ensure state updates are processed
            setTimeout(() => {
              try {
                // Use router.replace instead of reset since reset is not available in expo-router
                // This navigates to the dashboard screen without adding to history
                router.replace('/dashboard');
              } catch (navError) {
                throw new NavigationError(`Failed to navigate to dashboard: ${navError instanceof Error ? navError.message : 'Unknown error'}`);
              }
            }, 500);
          } catch (error) {
            // Show error to user
            Alert.alert(
              "Navigation Error",
              "There was an issue navigating to the dashboard. Your preferences have been saved successfully.",
              [{
                text: "Try Again",
                onPress: () => {
                  try {
                    router.replace('/dashboard');
                  } catch (retryError) {
                    Alert.alert(
                      "Navigation Failed",
                      "Please try navigating to the dashboard manually.",
                      [{ text: "OK" }]
                    );
                  }
                }
              }]
            );
          }
        })(); // Close and execute the async IIFE
      }

      return true;
    } catch (error) {
      // Handle different error types
      let errorMessage = 'An unexpected error occurred while saving your preferences.';
      let errorTitle = 'Error';

      if (error instanceof PreferencesSaveError) {
        errorTitle = 'Preferences Save Error';
        errorMessage = error.message || 'Failed to save your preferences. Please try again.';
      } else if (error instanceof NavigationError) {
        errorTitle = 'Navigation Error';
        errorMessage = error.message || 'There was an issue navigating after saving your preferences.';
      } else if (error instanceof PropertyMatchingError) {
        errorTitle = 'Property Matching Error';
        errorMessage = error.message || 'Your preferences were saved, but we couldn\'t find matching properties.';
      }

      // Log only critical errors
      LogService.error('PreferencesScreen', 'Exception in handleSavePreferences', {
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorType: error instanceof Error ? error.name : 'Unknown'
      });

      // Provide haptic feedback for error
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);

      // Show error alert to user
      Alert.alert(
        errorTitle,
        errorMessage,
        [{ text: "OK" }]
      );

      return false;
    } finally {
      // Reset loading states
      setIsSaving(false);
      setSavingStage('idle');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        showBackButton={true}
        onBack={() => router.back()}
      />

      {isLoading || isSaving ? (
        <Animated.View
          style={styles.loadingContainer}
          entering={FadeInUp.duration(600)}
        >
          <LinearGradient
            colors={['rgba(67, 97, 238, 0.1)', 'rgba(114, 9, 183, 0.1)']}
            style={styles.loadingCard}
          >
            <View style={styles.loadingIconContainer}>
              <ActivityIndicator size="large" color={THEME.primary} />
            </View>
            <Text style={styles.loadingText}>
              {isSaving ? (
                savingStage === 'saving' ? 'Saving your preferences...' :
                  savingStage === 'matching' ? 'Finding matching properties...' :
                    savingStage === 'navigating' ? 'Redirecting to dashboard...' :
                      'Processing your preferences...'
              ) : 'Loading...'}
            </Text>
            {isSaving && saveAttemptCount > 1 && (
              <Text style={styles.loadingSubtext}>
                Attempt {saveAttemptCount} of 3
              </Text>
            )}
          </LinearGradient>
        </Animated.View>
      ) : (
        <Animated.View
          style={styles.contentContainer}
          entering={FadeInUp.duration(600).delay(200)}
        >
          <PreferencesOnboarding isOnboarding={true}>
            <SmartPreferencesWizard
              onComplete={handleSavePreferences}
              initialPreferences={user?.preferences}
              onValidationChange={() => { }}
            />
          </PreferencesOnboarding>
        </Animated.View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
  },
  backButton: {
    padding: 8,
  },
  backButtonContainer: {
    width: 40,
    height: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholder: {
    width: 40,
  },
  headerCenter: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: 'center',
  },
  logoContainer: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: THEME.dark,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: "bold",
    color: THEME.primary,
  },
  headerTextContainer: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: THEME.light,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingCard: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 32,
    alignItems: 'center',
    shadowColor: THEME.dark,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    minWidth: 280,
  },
  loadingIconContainer: {
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 18,
    color: THEME.dark,
    textAlign: 'center',
    fontWeight: '600',
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: 'center',
    fontWeight: '500',
  },
});

