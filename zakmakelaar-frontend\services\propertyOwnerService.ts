import { authService } from './authService';
import { apiService } from './api';
import { getApiBaseUrl } from '../config/api';

export interface PropertyOwnerRegistrationData {
  businessRegistration: string;
  companyName: string;
  address: string;
  phone: string;
  website?: string;
  description?: string;
}

export interface PropertyData {
  title: string;
  description: string;
  address: {
    street: string;
    houseNumber: string;
    postalCode: string;
    city: string;
    province: string;
  };
  propertyType: 'apartment' | 'house' | 'studio' | 'room';
  size: number;
  rooms: number;
  bedrooms: number;
  bathrooms: number;
  rent: {
    amount: number;
    currency: string;
    deposit: number;
    additionalCosts: {
      utilities: number;
      serviceCharges: number;
      parking: number;
      other: number;
    };
  };
  features: {
    furnished: boolean;
    interior: 'kaal' | 'gestoffeerd' | 'gemeubileerd';
    parking: boolean;
    balcony: boolean;
    garden: boolean;
    elevator: boolean;
    energyLabel: string;
  };
  policies: {
    petsAllowed: boolean;
    smokingAllowed: boolean;
    studentsAllowed: boolean;
    expatFriendly: boolean;
    minimumIncome: number;
    maximumOccupants: number;
  };
  status: string;
  images?: {
    url: string;
    caption: string;
    isPrimary: boolean;
  }[];
  availabilityDate?: string;
}

export interface TenantApplication {
  id: string;
  propertyId: string;
  applicantName: string;
  applicantEmail: string;
  applicantPhone: string;
  moveInDate: string;
  duration: string;
  budget: number;
  employmentStatus: string;
  monthlyIncome: number;
  references: string[];
  message: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  documents: string[];
}

interface Document {
  id?: string;
  name: string;
  type: 'id' | 'business' | 'address' | 'other';
  uri: string;
  status: 'pending' | 'approved' | 'rejected';
  uploadDate?: string;
  fileType?: string;
}

class PropertyOwnerService {
  private baseUrl = `${getApiBaseUrl()}/property-owner`;

  private async getAuthHeaders() {
    const token = await authService.getAuthToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    };
  }
  
  private async getMultipartHeaders() {
    const token = await authService.getAuthToken();
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  async registerPropertyOwner(data: PropertyOwnerRegistrationData) {
    try {
      const response = await fetch(`${this.baseUrl}/register`, {
        method: 'POST',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Registration failed');
      }

      return await response.json();
    } catch (error) {
      console.error('Property owner registration error:', error);
      throw error;
    }
  }

  async verifyPropertyOwner(verificationData: {
    documents: string[];
    businessLicense?: string;
    taxId?: string;
  }) {
    try {
      const response = await fetch(`${this.baseUrl}/verify`, {
        method: 'POST',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(verificationData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Verification failed');
      }

      return await response.json();
    } catch (error) {
      console.error('Property owner verification error:', error);
      throw error;
    }
  }

  async getDashboardData() {
    try {
      return await apiService.get('/property-owner/dashboard');
    } catch (error) {
      console.error('Dashboard data error:', error);
      throw error;
    }
  }

  async getProperties() {
    try {
      return await apiService.get('/property-owner/properties');
    } catch (error) {
      console.error('Properties error:', error);
      throw error;
    }
  }

  async getPropertyDetails(propertyId: string) {
    try {
      return await apiService.get(`/property-owner/properties/${propertyId}`);
    } catch (error) {
      console.error('Property details error:', error);
      throw error;
    }
  }

  async addProperty(propertyData: PropertyData) {
    try {
      return await apiService.post('/property-owner/properties', propertyData);
    } catch (error) {
      console.error('Add property error:', error);
      throw error;
    }
  }

  async updateProperty(propertyId: string, propertyData: Partial<PropertyData>) {
    try {
      const response = await fetch(`${this.baseUrl}/properties/${propertyId}`, {
        method: 'PUT',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(propertyData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update property');
      }

      return await response.json();
    } catch (error) {
      console.error('Update property error:', error);
      throw error;
    }
  }

  async deleteProperty(propertyId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/properties/${propertyId}`, {
        method: 'DELETE',
        headers: await this.getAuthHeaders(),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete property');
      }

      return await response.json();
    } catch (error) {
      console.error('Delete property error:', error);
      throw error;
    }
  }

  async getApplications() {
    try {
      const response = await fetch(`${this.baseUrl}/applications`, {
        headers: await this.getAuthHeaders(),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to load applications');
      }

      return await response.json();
    } catch (error) {
      console.error('Applications error:', error);
      throw error;
    }
  }

  async getApplication(applicationId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/applications/${applicationId}`, {
        headers: await this.getAuthHeaders(),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to load application');
      }

      return await response.json();
    } catch (error) {
      console.error('Application error:', error);
      throw error;
    }
  }

  async updateApplicationStatus(applicationId: string, status: 'approved' | 'rejected', notes?: string) {
    try {
      const response = await fetch(`${this.baseUrl}/applications/${applicationId}/status`, {
        method: 'PUT',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify({ status, notes }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update application status');
      }

      return await response.json();
    } catch (error) {
      console.error('Update application status error:', error);
      throw error;
    }
  }

  async screenTenant(screeningData: {
    applicationId: string;
    creditScore?: number;
    incomeVerification?: boolean;
    employmentVerification?: boolean;
    referenceChecks?: boolean;
    backgroundCheck?: boolean;
    notes?: string;
  }) {
    try {
      const response = await fetch(`${this.baseUrl}/screen-tenant`, {
        method: 'POST',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(screeningData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to screen tenant');
      }

      return await response.json();
    } catch (error) {
      console.error('Screen tenant error:', error);
      throw error;
    }
  }

  async uploadDocument(file: File) {
    try {
      const formData = new FormData();
      formData.append('document', file);

      const response = await fetch(`${this.baseUrl}/documents`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${await authService.getAuthToken()}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to upload document');
      }

      return await response.json();
    } catch (error) {
      console.error('Upload document error:', error);
      throw error;
    }
  }

  async getOwnerProfile() {
    try {
      const response = await fetch(`${this.baseUrl}/profile`, {
        headers: await this.getAuthHeaders(),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to load profile');
      }

      return await response.json();
    } catch (error) {
      console.error('Profile error:', error);
      throw error;
    }
  }

  async updateOwnerProfile(profileData: any) {
    try {
      const response = await fetch(`${this.baseUrl}/profile`, {
        method: 'PUT',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update profile');
      }

      return await response.json();
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }

  async getVerificationDocuments() {
    try {
      const response = await fetch(`${this.baseUrl}/verification/documents`, {
        method: 'GET',
        headers: await this.getAuthHeaders(),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch verification documents');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching verification documents:', error);
      throw error;
    }
  }

  async uploadVerificationDocument(document: Document) {
    try {
      // Create form data
      const formData = new FormData();
      
      // Add file
      const fileUri = document.uri;
      const fileName = document.name;
      const fileType = document.fileType || 'image/jpeg';
      
      // @ts-ignore - FormData expects specific structure
      formData.append('file', {
        uri: fileUri,
        name: fileName,
        type: fileType,
      });
      
      // Add document metadata
      formData.append('type', document.type);
      formData.append('name', document.name);
      
      const response = await fetch(`${this.baseUrl}/verification/documents/upload`, {
        method: 'POST',
        headers: await this.getMultipartHeaders(),
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to upload document');
      }

      return await response.json();
    } catch (error) {
      console.error('Error uploading verification document:', error);
      throw error;
    }
  }

  async deleteVerificationDocument(documentId: string) {
    try {
      const response = await fetch(`${this.baseUrl}/verification/documents/${documentId}`, {
        method: 'DELETE',
        headers: await this.getAuthHeaders(),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete document');
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting verification document:', error);
      throw error;
    }
  }
}

export const propertyOwnerService = new PropertyOwnerService();
