# Auto Application API Documentation

## Overview

This document describes the API endpoints implemented for the auto-application management system. The implementation includes REST endpoints for managing auto-application settings, viewing application history, uploading documents, and real-time WebSocket updates.

## Implemented Endpoints

### 1. Enable/Disable Auto-Application

#### POST /api/auto-application/enable
- **Description**: Enable auto-application functionality for the authenticated user
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK with updated settings
- **WebSocket Event**: Sends `auto-application:status-update` to subscribed clients

#### POST /api/auto-application/disable
- **Description**: Disable auto-application functionality for the authenticated user
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK
- **WebSocket Event**: Sends `auto-application:status-update` to subscribed clients

### 2. Settings Management

#### GET /api/auto-application/settings
- **Description**: Retrieve current auto-application settings for the user
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK with settings object

#### POST /api/auto-application/settings
- **Description**: Update auto-application settings
- **Authentication**: Required (Bearer token)
- **Request Body**: Settings object with validation
- **Response**: 200 OK with updated settings

### 3. Application History and Status

#### GET /api/auto-application/history
- **Description**: Retrieve comprehensive application history with detailed status tracking
- **Authentication**: Required (Bearer token)
- **Query Parameters**:
  - `status`: Filter by application status
  - `limit`: Number of results (default: 20, max: 100)
  - `offset`: Pagination offset
  - `dateFrom`: Filter from date
  - `dateTo`: Filter to date
- **Response**: 200 OK with applications array, total count, and summary statistics

#### GET /api/auto-application/status
- **Description**: Get real-time auto-application status
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK with current status including:
  - Enabled/disabled state
  - Pause status
  - Current queue statistics
  - Today's activity
  - System health indicators

### 4. Document Management

#### POST /api/auto-application/documents/upload
- **Description**: Upload required documents for auto-application
- **Authentication**: Required (Bearer token)
- **Content-Type**: multipart/form-data
- **Request Body**:
  - `documents`: Array of files (max 5 files, 10MB each)
  - `documentType`: Type of document (income_proof, employment_contract, bank_statement, id_document, rental_reference)
  - `expiryDate`: Optional expiry date
- **Response**: 201 Created with uploaded documents and profile completeness
- **WebSocket Event**: Sends `document:update` to user

#### GET /api/auto-application/documents
- **Description**: Retrieve user's auto-application documents
- **Authentication**: Required (Bearer token)
- **Query Parameters**:
  - `type`: Filter by document type
- **Response**: 200 OK with documents, required documents list, and completeness percentage

#### DELETE /api/auto-application/documents/{documentId}
- **Description**: Delete a specific document
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK with updated profile completeness
- **WebSocket Event**: Sends `document:update` to user

### 5. Queue Management

#### GET /api/auto-application/queue
- **Description**: Get user's application queue
- **Authentication**: Required (Bearer token)
- **Query Parameters**:
  - `status`: Filter by queue status
  - `limit`: Number of results
  - `offset`: Pagination offset
- **Response**: 200 OK with queue items

#### POST /api/auto-application/queue
- **Description**: Add listing to application queue
- **Authentication**: Required (Bearer token)
- **Request Body**:
  - `listingId`: Property listing ID
  - `listingUrl`: Property listing URL
  - `priority`: Priority level (1-10)
- **Response**: 201 Created with queue item

#### DELETE /api/auto-application/queue/{queueId}
- **Description**: Remove application from queue
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK

### 6. Statistics and Monitoring

#### GET /api/auto-application/stats
- **Description**: Get comprehensive application statistics
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK with statistics including:
  - Total applications
  - Success/failure counts
  - Success rate percentage
  - Average response time
  - Applications by time period

#### GET /api/auto-application/results
- **Description**: Get application results with filtering
- **Authentication**: Required (Bearer token)
- **Query Parameters**:
  - `status`: Filter by result status
  - `limit`: Number of results
  - `offset`: Pagination offset
  - `dateFrom`: Filter from date
  - `dateTo`: Filter to date
- **Response**: 200 OK with results array

### 7. Process Control

#### POST /api/auto-application/process
- **Description**: Manually trigger application processing
- **Authentication**: Required (Bearer token)
- **Request Body**:
  - `queueId`: Optional specific queue item
  - `maxApplications`: Maximum applications to process
- **Response**: 200 OK with processing result

#### POST /api/auto-application/pause
- **Description**: Pause auto-application processing
- **Authentication**: Required (Bearer token)
- **Request Body**:
  - `duration`: Pause duration in minutes (optional)
  - `reason`: Reason for pausing (optional)
- **Response**: 200 OK with pause confirmation

#### POST /api/auto-application/resume
- **Description**: Resume paused auto-application processing
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK

### 8. Testing and Monitoring

#### POST /api/auto-application/test-form
- **Description**: Test form automation on a specific listing
- **Authentication**: Required (Bearer token)
- **Request Body**:
  - `listingUrl`: URL to test
  - `dryRun`: Whether to perform dry run (default: true)
- **Response**: 200 OK with test results

#### GET /api/auto-application/monitor/success-rates
- **Description**: Get detailed success rate analytics
- **Authentication**: Required (Bearer token)
- **Query Parameters**:
  - `timeframe`: Number of days to analyze (default: 30)
- **Response**: 200 OK with success rate data

#### GET /api/auto-application/monitor/patterns
- **Description**: Get application pattern analysis
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK with pattern insights and recommendations

#### GET /api/auto-application/monitor/performance
- **Description**: Get performance metrics
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK with performance data

#### GET /api/auto-application/monitor/dashboard
- **Description**: Get comprehensive dashboard data
- **Authentication**: Required (Bearer token)
- **Response**: 200 OK with dashboard overview, activity, recommendations, and alerts

## WebSocket Real-Time Updates

### Connection
- **URL**: `ws://localhost:3000` (or your server URL)
- **Authentication**: JWT token in auth object or Authorization header

### Subscription Events
- `subscribe:auto-application` - Subscribe to auto-application status updates
- `subscribe:queue-status` - Subscribe to queue status updates
- `subscribe:application-monitor` - Subscribe to application monitoring updates
- `unsubscribe:*` - Unsubscribe from specific updates

### Received Events
- `connected` - Connection confirmation
- `auto-application:status-update` - Auto-application status changes
- `queue:status-update` - Queue status changes
- `application:result` - Application submission results
- `application:progress` - Real-time application progress
- `document:update` - Document upload/delete confirmations
- `monitor:update` - Monitoring data updates
- `system:alert` - System alerts and warnings
- `system:notification` - System-wide notifications

## Error Handling

All endpoints follow consistent error response format:
```json
{
  "status": "error",
  "message": "Error description",
  "errors": [] // Validation errors if applicable
}
```

Common HTTP status codes:
- 200: Success
- 201: Created
- 400: Bad Request (validation errors)
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 413: Payload Too Large (file uploads)
- 429: Too Many Requests (rate limiting)
- 500: Internal Server Error

## Rate Limiting

- Document uploads: 10 uploads per 15 minutes
- General API calls: Standard rate limiting applies
- WebSocket connections: Per-user connection limits

## File Upload Specifications

### Supported Document Types
- PDF files (.pdf)
- Image files (.jpg, .jpeg, .png)
- Word documents (.doc, .docx)

### File Size Limits
- Maximum file size: 10MB per file
- Maximum files per upload: 5 files
- Total request size limit: 50MB

### Required Document Types
1. **income_proof**: Salary slips, income statements
2. **employment_contract**: Work contracts, employment letters
3. **bank_statement**: Recent bank statements (last 3 months)
4. **id_document**: Passport, national ID, driver's license
5. **rental_reference**: Previous rental references (optional)

## Integration Examples

### JavaScript/Node.js
```javascript
// REST API call
const response = await fetch('/api/auto-application/settings', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

// WebSocket connection
const socket = io('ws://localhost:3000', {
  auth: { token: userToken }
});

socket.emit('subscribe:auto-application');
socket.on('auto-application:status-update', (data) => {
  console.log('Status update:', data);
});
```

### React/Frontend
```javascript
// File upload
const formData = new FormData();
formData.append('documents', file);
formData.append('documentType', 'income_proof');

const response = await fetch('/api/auto-application/documents/upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

## Testing

Comprehensive integration tests are provided in:
- `src/tests/integration/autoApplicationAPI.integration.test.js`
- `src/tests/integration/websocket.integration.test.js`

Run tests with:
```bash
npm test -- --testPathPattern="autoApplicationAPI.integration.test.js"
```

## Security Considerations

1. **Authentication**: All endpoints require valid JWT tokens
2. **File Validation**: Uploaded files are validated for type and size
3. **Rate Limiting**: Prevents abuse of upload and API endpoints
4. **Input Validation**: All request data is validated using express-validator
5. **WebSocket Security**: Token-based authentication for WebSocket connections
6. **CORS**: Configured for allowed origins only

## Performance Optimizations

1. **Pagination**: Large result sets are paginated
2. **Caching**: Frequently accessed data is cached
3. **Database Indexing**: Optimized queries with proper indexes
4. **File Storage**: Efficient file storage and retrieval
5. **WebSocket Rooms**: Targeted message delivery to reduce overhead

## Monitoring and Logging

- All API calls are logged with request/response details
- WebSocket connections and events are tracked
- Error logging with stack traces for debugging
- Performance metrics collection
- User activity tracking for analytics

This implementation provides a comprehensive auto-application management system with real-time updates, document handling, and extensive monitoring capabilities.