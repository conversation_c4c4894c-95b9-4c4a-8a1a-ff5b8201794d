const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const ApplicationQueue = require('../models/ApplicationQueue');
const ApplicationResult = require('../models/ApplicationResult');
const User = require('../models/User');
const Listing = require('../models/Listing');
const aiService = require('./aiService');
const websocketService = require('./websocketService');
const documentVaultService = require('./documentVaultService');
const ErrorHandlingService = require('./errorHandlingService');
const { loggers } = require('./logger');

/**
 * AutoApplicationService - Core service for automated property applications
 * 
 * This service provides:
 * - Auto-application enabling/disabling with validation
 * - User settings management with persistence
 * - New listing processing and criteria matching
 * - Integration with AI service for application content generation
 * - Application queue management and status tracking
 */
class AutoApplicationService {
  constructor() {
    this.isProcessing = false;
    this.processingInterval = null;
    this.PROCESSING_INTERVAL_MS = 5 * 60 * 1000; // 5 minutes
    this.MAX_DAILY_APPLICATIONS = 20;
    this.MIN_APPLICATION_DELAY_MS = 2 * 60 * 1000; // 2 minutes
    this.MAX_APPLICATION_DELAY_MS = 10 * 60 * 1000; // 10 minutes
    this.errorHandlingService = new ErrorHandlingService();
  }

  /**
   * Enable auto-application for a user
   * @param {string} userId - User ID
   * @param {Object} settings - Auto-application settings
   * @returns {Promise<Object>} Updated settings
   */
  async enableAutoApplication(userId, settings) {
    try {
      loggers.app.info(`Enabling auto-application for user ${userId}`);

      // Validate user exists
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Validate required settings
      this._validateSettings(settings);

      // Find or create auto-application settings
      let autoSettings = await AutoApplicationSettings.findByUserId(userId);
      
      if (!autoSettings) {
        autoSettings = new AutoApplicationSettings({
          userId,
          enabled: false,
          settings: {},
          criteria: {},
          personalInfo: {},
          documents: [],
          statistics: {},
          status: {}
        });
      }

      // Update settings
      autoSettings.enabled = true;
      autoSettings.settings = {
        maxApplicationsPerDay: Math.min(settings.maxApplicationsPerDay || 5, this.MAX_DAILY_APPLICATIONS),
        applicationTemplate: settings.applicationTemplate || 'professional',
        autoSubmit: settings.autoSubmit !== false, // Default to true
        requireManualReview: settings.requireManualReview || false,
        notificationPreferences: {
          immediate: settings.notificationPreferences?.immediate !== false,
          daily: settings.notificationPreferences?.daily !== false,
          weekly: settings.notificationPreferences?.weekly || false
        },
        language: settings.language || 'english'
      };

      // Update criteria
      if (settings.criteria) {
        autoSettings.criteria = {
          maxPrice: settings.criteria.maxPrice,
          minRooms: settings.criteria.minRooms || 1,
          maxRooms: settings.criteria.maxRooms || 10,
          propertyTypes: settings.criteria.propertyTypes || [],
          locations: settings.criteria.locations || [],
          excludeKeywords: settings.criteria.excludeKeywords || [],
          includeKeywords: settings.criteria.includeKeywords || [],
          minSize: settings.criteria.minSize,
          maxSize: settings.criteria.maxSize,
          furnished: settings.criteria.furnished,
          petsAllowed: settings.criteria.petsAllowed,
          smokingAllowed: settings.criteria.smokingAllowed
        };
      }

      // Update personal info if provided
      if (settings.personalInfo) {
        autoSettings.personalInfo = {
          ...autoSettings.personalInfo,
          ...settings.personalInfo
        };
      }

      // Reset error count when enabling
      if (!autoSettings.status) {
        autoSettings.status = {};
      }
      autoSettings.status.errorCount = 0;
      autoSettings.status.lastError = undefined;
      autoSettings.status.pausedReason = undefined;
      autoSettings.status.pausedUntil = undefined;

      await autoSettings.save();

      // Start processing if not already running
      this._startProcessing();

      loggers.app.info(`Auto-application enabled for user ${userId}`);

      return this._formatSettingsResponse(autoSettings);

    } catch (error) {
      const recoveryResult = await this.errorHandlingService.handleError(error, {
        service: 'AutoApplicationService',
        method: 'enableAutoApplication',
        userId,
        operation: 'enable_auto_application'
      });
      
      loggers.app.error(`Error enabling auto-application for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Disable auto-application for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated settings
   */
  async disableAutoApplication(userId) {
    try {
      loggers.app.info(`Disabling auto-application for user ${userId}`);

      const autoSettings = await AutoApplicationSettings.findByUserId(userId);
      if (!autoSettings) {
        throw new Error('Auto-application settings not found');
      }

      autoSettings.enabled = false;
      autoSettings.status.isActive = false;
      autoSettings.status.pausedReason = 'Disabled by user';
      
      await autoSettings.save();

      // Cancel any pending applications for this user
      await ApplicationQueue.updateMany(
        { userId, status: 'pending' },
        { status: 'cancelled', processedAt: new Date() }
      );

      loggers.app.info(`Auto-application disabled for user ${userId}`);

      return this._formatSettingsResponse(autoSettings);

    } catch (error) {
      loggers.app.error(`Error disabling auto-application for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update user auto-application settings
   * @param {string} userId - User ID
   * @param {Object} updates - Settings updates
   * @returns {Promise<Object>} Updated settings
   */
  async updateUserSettings(userId, updates) {
    try {
      loggers.app.info(`Updating auto-application settings for user ${userId}`);

      const autoSettings = await AutoApplicationSettings.findByUserId(userId);
      if (!autoSettings) {
        throw new Error('Auto-application settings not found');
      }

      // Validate updates
      if (updates.settings) {
        this._validateSettingsUpdate(updates.settings);
      }

      // Update settings
      if (updates.settings) {
        autoSettings.settings = {
          ...autoSettings.settings,
          ...updates.settings,
          maxApplicationsPerDay: Math.min(
            updates.settings.maxApplicationsPerDay || autoSettings.settings.maxApplicationsPerDay,
            this.MAX_DAILY_APPLICATIONS
          )
        };
      }

      // Update criteria
      if (updates.criteria) {
        autoSettings.criteria = {
          ...autoSettings.criteria,
          ...updates.criteria
        };
      }

      // Update personal info
      if (updates.personalInfo) {
        autoSettings.personalInfo = {
          ...autoSettings.personalInfo,
          ...updates.personalInfo
        };
      }

      await autoSettings.save();

      loggers.app.info(`Auto-application settings updated for user ${userId}`);

      return this._formatSettingsResponse(autoSettings);

    } catch (error) {
      loggers.app.error(`Error updating auto-application settings for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get user auto-application settings
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User settings
   */
  async getUserSettings(userId) {
    try {
      const autoSettings = await AutoApplicationSettings.findByUserId(userId);
      if (!autoSettings) {
        return null;
      }

      return this._formatSettingsResponse(autoSettings);

    } catch (error) {
      loggers.app.error(`Error getting auto-application settings for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Process a new listing for auto-application
   * @param {Object} listing - New listing data
   * @returns {Promise<Array>} Array of created applications
   */
  async processNewListing(listing) {
    try {
      loggers.app.info(`Processing new listing for auto-application: ${listing._id}`);

      // Get all active auto-application users
      const activeUsers = await AutoApplicationSettings.findActiveUsers();
      const createdApplications = [];

      for (const userSettings of activeUsers) {
        try {
          // Check if user has reached daily limit
          if (userSettings.statistics.dailyApplicationCount >= userSettings.settings.maxApplicationsPerDay) {
            continue;
          }

          // Check if listing matches user criteria
          if (!userSettings.matchesCriteria(listing)) {
            continue;
          }

          // Check if user already applied to this listing
          const existingApplication = await ApplicationQueue.findOne({
            userId: userSettings.userId,
            listingId: listing._id
          });

          if (existingApplication) {
            continue;
          }

          // Generate application content
          const user = await User.findById(userSettings.userId);
          const applicationContent = await this._generateApplicationContent(listing, user, userSettings);

          // Create queue item
          const queueItem = new ApplicationQueue({
            userId: userSettings.userId,
            listingId: listing._id,
            listingUrl: listing.url,
            priority: this._calculatePriority(listing, userSettings),
            status: 'pending',
            attempts: 0,
            maxAttempts: 3,
            scheduledAt: this._calculateScheduledTime(),
            applicationData: {
              personalInfo: userSettings.personalInfo,
              documents: userSettings.documents.filter(doc => doc.uploaded && doc.verified),
              preferences: userSettings.settings
            },
            generatedContent: applicationContent,
            metadata: {
              formType: 'unknown',
              detectionMethods: [],
              processingTime: 0
            }
          });

          await queueItem.save();
          createdApplications.push(queueItem);

          loggers.app.info(`Created auto-application for user ${userSettings.userId}, listing ${listing._id}`);

        } catch (error) {
          loggers.app.error(`Error processing listing ${listing._id} for user ${userSettings.userId}:`, error);
          // Continue with other users
        }
      }

      loggers.app.info(`Created ${createdApplications.length} auto-applications for listing ${listing._id}`);
      return createdApplications;

    } catch (error) {
      loggers.app.error(`Error processing new listing ${listing._id}:`, error);
      throw error;
    }
  }

  /**
   * Generate application content using AI service
   * @param {Object} listing - Listing data
   * @param {Object} user - User data
   * @param {Object} settings - Auto-application settings
   * @returns {Promise<Object>} Generated application content
   */
  async _generateApplicationContent(listing, user, settings) {
    try {
      // Prepare user profile for AI service
      const userProfile = {
        name: settings.personalInfo.fullName || user.profile?.fullName || user.email,
        income: settings.personalInfo.monthlyIncome || user.profile?.employment?.monthlyIncome,
        occupation: settings.personalInfo.occupation || user.profile?.employment?.occupation || 'Professional',
        employer: settings.personalInfo.employer || user.profile?.employment?.employer,
        nationality: settings.personalInfo.nationality || user.profile?.nationality,
        moveInDate: settings.personalInfo.moveInDate,
        leaseDuration: settings.personalInfo.leaseDuration,
        numberOfOccupants: settings.personalInfo.numberOfOccupants || 1,
        hasGuarantor: settings.personalInfo.hasGuarantor || false
      };

      // Generate application message using enhanced AI service
      const language = settings.settings.language || 'dutch';
      const aiResponse = await aiService.generateAutoApplicationLetter(
        listing,
        userProfile,
        settings.settings,
        language
      );

      return {
        subject: aiResponse.subject || `Application for ${listing.title}`,
        message: aiResponse.message,
        personalizedElements: aiResponse.personalizedElements || [],
        template: aiResponse.template,
        language: aiResponse.language,
        wordCount: aiResponse.wordCount,
        estimatedReadTime: aiResponse.estimatedReadTime,
        generatedAt: aiResponse.generatedAt
      };

    } catch (error) {
      loggers.app.error('Error generating application content:', error);
      
      // Fallback to basic template if AI fails
      return this._generateFallbackContent(listing, user, settings);
    }
  }

  /**
   * Generate fallback application content when AI fails
   * @param {Object} listing - Listing data
   * @param {Object} user - User data
   * @param {Object} settings - Auto-application settings
   * @returns {Object} Basic application content
   */
  _generateFallbackContent(listing, user, settings) {
    const name = settings.personalInfo.fullName || user.profile?.fullName || 'Applicant';
    const occupation = settings.personalInfo.occupation || user.profile?.employment?.occupation || 'Professional';
    
    const message = `Dear landlord,

I am interested in applying for the property at ${listing.location} (${listing.title}).

I am a ${occupation} with a stable income and I am looking for a new home. I believe this property would be perfect for my needs.

I am available for a viewing at your convenience and can provide all necessary documentation.

Thank you for your consideration.

Best regards,
${name}`;

    return {
      subject: `Application for ${listing.title}`,
      message,
      personalizedElements: ['Basic template'],
      template: 'fallback',
      language: settings.settings.language,
      generatedAt: new Date()
    };
  }

  /**
   * Calculate priority for application queue
   * @param {Object} listing - Listing data
   * @param {Object} settings - User settings
   * @returns {number} Priority score (higher = more priority)
   */
  _calculatePriority(listing, settings) {
    let priority = 50; // Base priority

    // Price-based priority (lower price = higher priority)
    if (listing.price && settings.criteria.maxPrice) {
      const priceMatch = listing.price.match(/[\d,]+/);
      if (priceMatch) {
        const listingPrice = parseInt(priceMatch[0].replace(/,/g, ''));
        const priceRatio = listingPrice / settings.criteria.maxPrice;
        priority += Math.max(0, (1 - priceRatio) * 20);
      }
    }

    // Location-based priority
    if (settings.criteria.locations.length > 0) {
      const hasPreferredLocation = settings.criteria.locations.some(location =>
        listing.location.toLowerCase().includes(location.toLowerCase())
      );
      if (hasPreferredLocation) {
        priority += 15;
      }
    }

    // Property type priority
    if (settings.criteria.propertyTypes.length > 0) {
      const hasPreferredType = settings.criteria.propertyTypes.some(type =>
        listing.propertyType && listing.propertyType.toLowerCase().includes(type.toLowerCase())
      );
      if (hasPreferredType) {
        priority += 10;
      }
    }

    // Recency priority (newer listings get higher priority)
    const listingAge = Date.now() - new Date(listing.dateAdded).getTime();
    const ageInHours = listingAge / (1000 * 60 * 60);
    if (ageInHours < 1) {
      priority += 20; // Very new listing
    } else if (ageInHours < 6) {
      priority += 10; // Recent listing
    }

    return Math.round(priority);
  }

  /**
   * Calculate scheduled time for application with random delay
   * @returns {Date} Scheduled time
   */
  _calculateScheduledTime() {
    const delay = Math.random() * (this.MAX_APPLICATION_DELAY_MS - this.MIN_APPLICATION_DELAY_MS) + this.MIN_APPLICATION_DELAY_MS;
    return new Date(Date.now() + delay);
  }

  /**
   * Get application status for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Application status
   */
  async getApplicationStatus(userId) {
    try {
      const autoSettings = await AutoApplicationSettings.findByUserId(userId);
      if (!autoSettings) {
        return {
          enabled: false,
          isActive: false,
          statistics: {
            totalApplications: 0,
            successfulApplications: 0,
            pendingApplications: 0,
            dailyApplicationCount: 0,
            successRate: 0
          }
        };
      }

      // Get recent applications
      const recentApplications = await ApplicationQueue.find({
        userId,
        createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
      })
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('listingId', 'title location price');

      return {
        enabled: autoSettings.enabled,
        isActive: autoSettings.status.isActive,
        canAutoApply: autoSettings.canAutoApply,
        dailyApplicationsRemaining: autoSettings.dailyApplicationsRemaining,
        statistics: autoSettings.statistics,
        status: {
          pausedReason: autoSettings.status.pausedReason,
          pausedUntil: autoSettings.status.pausedUntil,
          lastProcessed: autoSettings.status.lastProcessed,
          errorCount: autoSettings.status.errorCount,
          lastError: autoSettings.status.lastError
        },
        recentApplications: recentApplications.map(app => ({
          id: app._id,
          listingTitle: app.listingId?.title || 'Unknown',
          listingLocation: app.listingId?.location || 'Unknown',
          status: app.status,
          scheduledAt: app.scheduledAt,
          processedAt: app.processedAt,
          attempts: app.attempts
        }))
      };

    } catch (error) {
      loggers.app.error(`Error getting application status for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Start the processing interval
   * @private
   */
  _startProcessing() {
    if (!this.processingInterval) {
      this.processingInterval = setInterval(() => {
        this._processQueue().catch(error => {
          loggers.app.error('Error in queue processing interval:', error);
        });
      }, this.PROCESSING_INTERVAL_MS);
      
      loggers.app.info('Auto-application processing started');
    }
  }

  /**
   * Stop the processing interval
   * @private
   */
  _stopProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      loggers.app.info('Auto-application processing stopped');
    }
  }

  /**
   * Process the application queue
   * @private
   */
  async _processQueue() {
    if (this.isProcessing) {
      return; // Already processing
    }

    try {
      this.isProcessing = true;
      
      // Get pending applications that are ready to be processed
      const pendingApplications = await ApplicationQueue.find({
        status: 'pending',
        scheduledAt: { $lte: new Date() }
      })
      .sort({ priority: -1, scheduledAt: 1 })
      .limit(10); // Process up to 10 at a time

      if (pendingApplications.length === 0) {
        return;
      }

      loggers.app.info(`Processing ${pendingApplications.length} pending applications`);

      for (const application of pendingApplications) {
        try {
          // Mark as processing
          application.status = 'processing';
          application.processedAt = new Date();
          await application.save();

          // For now, just mark as completed since we don't have form automation yet
          // In the full implementation, this would trigger the form automation engine
          application.status = 'completed';
          await application.save();

          // Update user statistics
          const autoSettings = await AutoApplicationSettings.findByUserId(application.userId);
          if (autoSettings) {
            await autoSettings.incrementApplicationCount();
          }

          loggers.app.info(`Processed application ${application._id}`);

        } catch (error) {
          // Use comprehensive error handling
          const recoveryResult = await this.errorHandlingService.handleError(error, {
            service: 'AutoApplicationService',
            method: 'processQueue',
            queueItemId: application._id,
            userId: application.userId,
            attemptNumber: application.attempts + 1,
            operation: 'process_application'
          });
          
          loggers.app.error(`Error processing application ${application._id}:`, error);
          
          // If error handling didn't schedule a retry, handle it manually
          if (!recoveryResult.retryScheduled) {
            application.status = 'failed';
            application.attempts += 1;
            application.errors.push(error.message);
            await application.save();
          }
        }
      }

    } catch (error) {
      loggers.app.error('Error processing application queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Validate settings update object (partial validation)
   * @param {Object} settings - Settings to validate
   * @private
   */
  _validateSettingsUpdate(settings) {
    if (!settings) {
      return; // No settings to validate
    }

    if (settings.maxApplicationsPerDay && (settings.maxApplicationsPerDay < 1 || settings.maxApplicationsPerDay > this.MAX_DAILY_APPLICATIONS)) {
      throw new Error(`Max applications per day must be between 1 and ${this.MAX_DAILY_APPLICATIONS}`);
    }

    if (settings.applicationTemplate && !['professional', 'casual', 'student', 'expat'].includes(settings.applicationTemplate)) {
      throw new Error('Invalid application template');
    }

    if (settings.language && !['dutch', 'english'].includes(settings.language)) {
      throw new Error('Invalid language');
    }
  }

  /**
   * Validate settings object
   * @param {Object} settings - Settings to validate
   * @private
   */
  _validateSettings(settings) {
    if (!settings) {
      throw new Error('Settings are required');
    }

    if (settings.maxApplicationsPerDay && (settings.maxApplicationsPerDay < 1 || settings.maxApplicationsPerDay > this.MAX_DAILY_APPLICATIONS)) {
      throw new Error(`Max applications per day must be between 1 and ${this.MAX_DAILY_APPLICATIONS}`);
    }

    if (settings.applicationTemplate && !['professional', 'casual', 'student', 'expat'].includes(settings.applicationTemplate)) {
      throw new Error('Invalid application template');
    }

    if (settings.language && !['dutch', 'english'].includes(settings.language)) {
      throw new Error('Invalid language');
    }

    if (settings.criteria) {
      if (!settings.criteria.maxPrice || settings.criteria.maxPrice <= 0) {
        throw new Error('Max price is required and must be greater than 0');
      }

      if (settings.criteria.minRooms && settings.criteria.minRooms < 1) {
        throw new Error('Min rooms must be at least 1');
      }

      if (settings.criteria.maxRooms && settings.criteria.maxRooms < 1) {
        throw new Error('Max rooms must be at least 1');
      }

      if (settings.criteria.minRooms && settings.criteria.maxRooms && settings.criteria.minRooms > settings.criteria.maxRooms) {
        throw new Error('Min rooms cannot be greater than max rooms');
      }
    }
  }

  /**
   * Format settings response for API
   * @param {Object} autoSettings - Auto-application settings
   * @returns {Object} Formatted response
   * @private
   */
  _formatSettingsResponse(autoSettings) {
    return {
      id: autoSettings._id,
      userId: autoSettings.userId,
      enabled: autoSettings.enabled,
      isActive: autoSettings.status.isActive,
      canAutoApply: autoSettings.canAutoApply,
      settings: autoSettings.settings,
      criteria: autoSettings.criteria,
      personalInfo: {
        ...(autoSettings.personalInfo || {}),
        // Don't expose sensitive info in API response
        monthlyIncome: (autoSettings.personalInfo && autoSettings.personalInfo.monthlyIncome) ? '[HIDDEN]' : undefined
      },
      documents: (autoSettings.documents || []).map(doc => ({
        type: doc.type,
        required: doc.required,
        uploaded: doc.uploaded,
        verified: doc.verified
      })),
      statistics: autoSettings.statistics,
      status: {
        isActive: autoSettings.status ? autoSettings.status.isActive : false,
        pausedReason: autoSettings.status ? autoSettings.status.pausedReason : undefined,
        pausedUntil: autoSettings.status ? autoSettings.status.pausedUntil : undefined,
        lastProcessed: autoSettings.status ? autoSettings.status.lastProcessed : undefined,
        errorCount: autoSettings.status ? autoSettings.status.errorCount : 0
      },
      isProfileComplete: autoSettings.isProfileComplete,
      documentsComplete: autoSettings.documentsComplete,
      dailyApplicationsRemaining: autoSettings.dailyApplicationsRemaining,
      createdAt: autoSettings.createdAt,
      updatedAt: autoSettings.updatedAt
    };
  }

  /**
   * Shutdown the service gracefully
   */
  shutdown() {
    this._stopProcessing();
    loggers.app.info('AutoApplicationService shutdown complete');
  }
  /**
   * Enable auto-application for a user (simplified version)
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated settings
   */
  async enableAutoApplication(userId) {
    try {
      loggers.app.info(`Enabling auto-application for user ${userId}`);

      const settings = await this.updateSettings(userId, { isEnabled: true });
      
      // Send WebSocket notification
      websocketService.sendAutoApplicationUpdate(userId, {
        action: 'enabled',
        message: 'Auto-application has been enabled',
        settings: settings
      });

      return settings;
    } catch (error) {
      loggers.app.error(`Error enabling auto-application for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Disable auto-application for a user
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async disableAutoApplication(userId) {
    try {
      loggers.app.info(`Disabling auto-application for user ${userId}`);

      await this.updateSettings(userId, { isEnabled: false });
      
      // Send WebSocket notification
      websocketService.sendAutoApplicationUpdate(userId, {
        action: 'disabled',
        message: 'Auto-application has been disabled'
      });

      loggers.app.info(`Auto-application disabled for user ${userId}`);
    } catch (error) {
      loggers.app.error(`Error disabling auto-application for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get application history with detailed status
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Application history
   */
  async getApplicationHistory(userId, options = {}) {
    try {
      const { status, limit = 20, offset = 0, dateFrom, dateTo } = options;

      // Build query
      const query = { userId };
      if (status) query.status = status;
      if (dateFrom || dateTo) {
        query.createdAt = {};
        if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
        if (dateTo) query.createdAt.$lte = new Date(dateTo);
      }

      // Get applications with property details
      const applications = await ApplicationResult.find(query)
        .sort({ createdAt: -1 })
        .limit(parseInt(limit))
        .skip(parseInt(offset))
        .populate('listingId', 'title location price')
        .lean();

      // Get total count
      const total = await ApplicationResult.countDocuments(query);

      // Calculate summary statistics
      const summary = await this._calculateApplicationSummary(userId, dateFrom, dateTo);

      // Add timeline information for each application
      const applicationsWithTimeline = applications.map(app => ({
        ...app,
        property: app.listingId || {
          title: 'Property details unavailable',
          location: 'Unknown',
          price: 'Unknown'
        },
        timeline: this._generateApplicationTimeline(app)
      }));

      return {
        applications: applicationsWithTimeline,
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        summary
      };
    } catch (error) {
      loggers.app.error(`Error getting application history for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Upload documents for auto-application
   * @param {string} userId - User ID
   * @param {Object} uploadData - Upload data
   * @returns {Promise<Object>} Upload result
   */
  async uploadDocuments(userId, uploadData) {
    try {
      const { files, documentType, expiryDate } = uploadData;

      loggers.app.info(`Uploading ${files.length} documents for user ${userId}, type: ${documentType}`);

      const uploadedDocuments = [];
      const errors = [];

      // Process each file
      for (const file of files) {
        try {
          const document = await documentVaultService.uploadDocument(
            userId,
            file,
            documentType,
            { expiryDate }
          );
          uploadedDocuments.push({
            documentId: document._id,
            filename: document.filename,
            type: document.type,
            size: document.size,
            uploadedAt: document.createdAt
          });
        } catch (error) {
          errors.push({
            filename: file.originalname,
            error: error.message
          });
        }
      }

      // Update profile completeness
      const profileCompleteness = await this._calculateProfileCompleteness(userId);

      // Send WebSocket notification
      websocketService.sendDocumentUpdate(userId, {
        action: 'uploaded',
        documents: uploadedDocuments,
        profileCompleteness,
        errors: errors.length > 0 ? errors : undefined
      });

      return {
        uploadedDocuments,
        profileCompleteness,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error) {
      loggers.app.error(`Error uploading documents for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get user's documents for auto-application
   * @param {string} userId - User ID
   * @param {string} type - Document type filter
   * @returns {Promise<Object>} Documents data
   */
  async getDocuments(userId, type) {
    try {
      const documents = await documentVaultService.getDocuments(userId, type);
      
      const requiredDocuments = [
        { type: 'income_proof', required: true, description: 'Proof of income (salary slip, contract)' },
        { type: 'employment_contract', required: true, description: 'Employment contract or work agreement' },
        { type: 'bank_statement', required: true, description: 'Recent bank statements (last 3 months)' },
        { type: 'id_document', required: true, description: 'Valid ID document (passport, ID card)' },
        { type: 'rental_reference', required: false, description: 'Previous rental reference (if applicable)' }
      ];

      // Map uploaded documents to required documents
      const documentsWithStatus = requiredDocuments.map(reqDoc => ({
        ...reqDoc,
        uploaded: documents.some(doc => doc.type === reqDoc.type),
        documents: documents.filter(doc => doc.type === reqDoc.type)
      }));

      const completeness = await this._calculateDocumentCompleteness(userId);

      return {
        documents: documents.map(doc => ({
          documentId: doc._id,
          filename: doc.filename,
          type: doc.type,
          size: doc.size,
          uploadedAt: doc.createdAt,
          expiryDate: doc.expiryDate,
          verified: doc.verified
        })),
        requiredDocuments: documentsWithStatus,
        completeness
      };
    } catch (error) {
      loggers.app.error(`Error getting documents for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a document
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID
   * @returns {Promise<Object>} Result
   */
  async deleteDocument(userId, documentId) {
    try {
      await documentVaultService.deleteDocument(documentId, userId);
      
      const profileCompleteness = await this._calculateProfileCompleteness(userId);

      // Send WebSocket notification
      websocketService.sendDocumentUpdate(userId, {
        action: 'deleted',
        documentId,
        profileCompleteness
      });

      return { profileCompleteness };
    } catch (error) {
      loggers.app.error(`Error deleting document ${documentId} for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get real-time status for user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Status data
   */
  async getStatus(userId) {
    try {
      const settings = await this.getSettings(userId);
      const queueStats = await this._getQueueStats(userId);
      const todaysActivity = await this._getTodaysActivity(userId);
      const systemHealth = await this._getSystemHealth(userId);

      return {
        isEnabled: settings?.isEnabled || false,
        isPaused: settings?.isPaused || false,
        pausedUntil: settings?.pausedUntil,
        currentQueue: queueStats,
        todaysActivity,
        systemHealth
      };
    } catch (error) {
      loggers.app.error(`Error getting status for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate application summary statistics
   * @private
   */
  async _calculateApplicationSummary(userId, dateFrom, dateTo) {
    const query = { userId };
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    const [total, successful, failed, pending] = await Promise.all([
      ApplicationResult.countDocuments(query),
      ApplicationResult.countDocuments({ ...query, status: 'success' }),
      ApplicationResult.countDocuments({ ...query, status: 'failed' }),
      ApplicationQueue.countDocuments({ userId, status: 'pending' })
    ]);

    return {
      totalApplications: total,
      successfulApplications: successful,
      failedApplications: failed,
      pendingApplications: pending,
      successRate: total > 0 ? Math.round((successful / total) * 100) : 0
    };
  }

  /**
   * Generate application timeline
   * @private
   */
  _generateApplicationTimeline(application) {
    const timeline = [
      {
        status: 'queued',
        timestamp: application.createdAt,
        message: 'Application added to queue'
      }
    ];

    if (application.status === 'success') {
      timeline.push({
        status: 'submitted',
        timestamp: application.submittedAt || application.updatedAt,
        message: 'Application submitted successfully'
      });
    } else if (application.status === 'failed') {
      timeline.push({
        status: 'failed',
        timestamp: application.updatedAt,
        message: application.errorDetails?.message || 'Application failed'
      });
    }

    return timeline;
  }

  /**
   * Calculate profile completeness
   * @private
   */
  async _calculateProfileCompleteness(userId) {
    try {
      const settings = await this.getSettings(userId);
      const documents = await documentVaultService.getDocuments(userId);
      
      let completeness = 0;
      
      // Personal info completeness (40%)
      if (settings?.applicationTemplate?.personalInfo) {
        const personalInfo = settings.applicationTemplate.personalInfo;
        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'dateOfBirth'];
        const completedFields = requiredFields.filter(field => personalInfo[field]);
        completeness += (completedFields.length / requiredFields.length) * 40;
      }
      
      // Document completeness (60%)
      const requiredDocTypes = ['income_proof', 'employment_contract', 'bank_statement', 'id_document'];
      const uploadedTypes = [...new Set(documents.map(doc => doc.type))];
      const docCompleteness = requiredDocTypes.filter(type => uploadedTypes.includes(type)).length;
      completeness += (docCompleteness / requiredDocTypes.length) * 60;
      
      return Math.round(completeness);
    } catch (error) {
      loggers.app.error(`Error calculating profile completeness for user ${userId}:`, error);
      return 0;
    }
  }

  /**
   * Calculate document completeness
   * @private
   */
  async _calculateDocumentCompleteness(userId) {
    try {
      const documents = await documentVaultService.getDocuments(userId);
      const requiredDocTypes = ['income_proof', 'employment_contract', 'bank_statement', 'id_document'];
      const uploadedTypes = [...new Set(documents.map(doc => doc.type))];
      const completeness = requiredDocTypes.filter(type => uploadedTypes.includes(type)).length;
      return Math.round((completeness / requiredDocTypes.length) * 100);
    } catch (error) {
      loggers.app.error(`Error calculating document completeness for user ${userId}:`, error);
      return 0;
    }
  }

  /**
   * Get queue statistics
   * @private
   */
  async _getQueueStats(userId) {
    try {
      const [pending, processing] = await Promise.all([
        ApplicationQueue.countDocuments({ userId, status: 'pending' }),
        ApplicationQueue.countDocuments({ userId, status: 'processing' })
      ]);

      const nextScheduled = await ApplicationQueue.findOne(
        { userId, status: 'pending' },
        { scheduledFor: 1 }
      ).sort({ scheduledFor: 1 });

      return {
        pending,
        processing,
        nextScheduled: nextScheduled?.scheduledFor
      };
    } catch (error) {
      loggers.app.error(`Error getting queue stats for user ${userId}:`, error);
      return { pending: 0, processing: 0, nextScheduled: null };
    }
  }

  /**
   * Get today's activity
   * @private
   */
  async _getTodaysActivity(userId) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const applicationsSubmitted = await ApplicationResult.countDocuments({
        userId,
        createdAt: { $gte: today, $lt: tomorrow }
      });

      const settings = await this.getSettings(userId);
      const dailyLimit = settings?.maxApplicationsPerDay || this.MAX_DAILY_APPLICATIONS;

      return {
        applicationsSubmitted,
        applicationsRemaining: Math.max(0, dailyLimit - applicationsSubmitted),
        dailyLimit,
        resetTime: tomorrow
      };
    } catch (error) {
      loggers.app.error(`Error getting today's activity for user ${userId}:`, error);
      return {
        applicationsSubmitted: 0,
        applicationsRemaining: 0,
        dailyLimit: this.MAX_DAILY_APPLICATIONS,
        resetTime: new Date()
      };
    }
  }

  /**
   * Get system health
   * @private
   */
  async _getSystemHealth(userId) {
    try {
      const lastWeek = new Date();
      lastWeek.setDate(lastWeek.getDate() - 7);

      const [lastSuccessful, recentErrors, recentBlocking] = await Promise.all([
        ApplicationResult.findOne(
          { userId, status: 'success' },
          { submittedAt: 1 }
        ).sort({ submittedAt: -1 }),
        ApplicationResult.countDocuments({
          userId,
          status: 'failed',
          createdAt: { $gte: lastWeek }
        }),
        ApplicationResult.countDocuments({
          userId,
          'errorDetails.blockingDetected': true,
          createdAt: { $gte: lastWeek }
        })
      ]);

      let status = 'healthy';
      if (recentBlocking > 0) status = 'error';
      else if (recentErrors > 5) status = 'warning';

      return {
        status,
        lastSuccessfulApplication: lastSuccessful?.submittedAt,
        recentErrors,
        blockingDetected: recentBlocking > 0
      };
    } catch (error) {
      loggers.app.error(`Error getting system health for user ${userId}:`, error);
      return {
        status: 'error',
        lastSuccessfulApplication: null,
        recentErrors: 0,
        blockingDetected: false
      };
    }
  }
}

module.exports = new AutoApplicationService();