const cheerio = require('cheerio');
const BaseScraper = require('./BaseScraper');
const { getScraperConfig } = require('./configs/scraperConfigs');
const { ErrorClassifier, ErrorRecovery, errorMetrics } = require('./errors/ScrapingErrors');
const { rateLimiter, antiDetection } = require('./utils/RateLimiter');

/**
 * Enhanced Huurwoningen scraper using the new architecture
 */
class HuurwoningenScraper extends BaseScraper {
  constructor() {
    const config = getScraperConfig('huurwoningen');
    super('huurwoningen', config);
  }

  /**
   * Get site-specific selectors
   */
  getSelectors() {
    return this.config.selectors;
  }

  /**
   * Generate search URLs based on configuration
   */
  getSearchUrls() {
    // Use the same URL pattern as the working V1 scraper
    return [
      "https://www.huurwoningen.nl/in/amsterdam/",
      "https://www.huurwoningen.nl/in/rotterdam/",
      "https://www.huurwoningen.nl/in/den-haag/",
      "https://www.huurwoningen.nl/in/utrecht/",
      "https://www.huurwoningen.nl/in/eindhoven/",
      "https://www.huurwoningen.nl/in/groningen/"
    ];
  }

  /**
   * Configure page with site-specific settings
   */
  async configurePage(page) {
    // Apply anti-detection measures
    await antiDetection.setupAntiDetection(page, this.siteName);

    // Set cookies to avoid cookie banners
    if (this.config.cookieSettings) {
      await page.setCookie(this.config.cookieSettings);
    }

    // Set Huurwoningen-specific headers
    await page.setExtraHTTPHeaders({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'nl-NL,nl;q=0.9,en;q=0.8',
      'Referer': 'https://www.huurwoningen.nl/'
    });

    // Allow all resources to load properly
    // Note: Resource blocking disabled to ensure page content loads
  }

  /**
   * Extract listing data from a single listing element
   */
  async extractListingData($, element) {
    try {
      const listing = {
        source: 'huurwoningen',
        scrapedAt: new Date()
      };

      // Extract basic information
      listing.title = this.extractText(element, this.selectors.title);
      listing.location = this.extractText(element, this.selectors.location);
      listing.price = this.extractPrice($, element);
      
      // Extract URL
      const urlElement = element.find(this.selectors.url);
      if (urlElement.length) {
        const relativeUrl = urlElement.attr('href');
        listing.url = relativeUrl ? 
          (relativeUrl.startsWith('http') ? relativeUrl : `${this.config.baseUrl}${relativeUrl}`) : null;
      }

      // Extract image
      const imageElement = element.find(this.selectors.image);
      if (imageElement.length) {
        listing.image = imageElement.attr('src') || imageElement.attr('data-src');
      }

      // Extract size and rooms using regex patterns
      const elementText = element.text();
      listing.size = this.extractSizeFromText(elementText);
      listing.rooms = this.extractRoomsFromText(elementText);

      // Clean up extracted data
      listing.title = this.cleanTitle(listing.title);
      listing.location = this.cleanLocation(listing.location);

      // Fetch additional details if URL is available
      if (listing.url && this.shouldFetchDetails()) {
        try {
          await rateLimiter.waitForDelay(this.siteName);
          const detailData = await this.fetchListingDetails(listing.url);
          Object.assign(listing, detailData);
        } catch (error) {
          console.warn(`Failed to fetch details for ${listing.url}:`, error.message);
        }
      }

      return this.validateListing(listing) ? listing : null;

    } catch (error) {
      console.error('Error extracting Huurwoningen listing:', error.message);
      return null;
    }
  }

  /**
   * Extract size from text using regex patterns
   */
  extractSizeFromText(text) {
    const sizePatterns = [
      /(\d+)\s*m²/i,
      /woonoppervlakte:\s*(\d+)\s*m²/i,
      /oppervlakte:\s*(\d+)\s*m²/i
    ];

    for (const pattern of sizePatterns) {
      const match = text.match(pattern);
      if (match) {
        return `${match[1]} m²`;
      }
    }

    return null;
  }

  /**
   * Extract rooms from text using regex patterns
   */
  extractRoomsFromText(text) {
    const roomPatterns = [
      /(\d+)\s*kamers?/i,
      /kamers?:\s*(\d+)/i,
      /(\d+)\s*slaapkamers?/i
    ];

    for (const pattern of roomPatterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * Fetch detailed information from listing page
   */
  async fetchListingDetails(url) {
    const browser = await this.getBrowserInstance();
    let page = null;

    try {
      page = await browser.newPage();
      await this.configurePage(page);

      await page.goto(url, {
        waitUntil: "networkidle2",
        timeout: this.config.timeout,
      });

      // Add human-like behavior
      await antiDetection.addHumanBehavior(page);

      const html = await page.content();
      const $ = cheerio.load(html);

      const details = {};

      // Extract description
      details.description = this.extractText($, this.selectors.detailSelectors.description);
      if (details.description) {
        details.description = details.description.replace(/\s+/g, ' ').substring(0, 1000);
      }

      // Extract property details from various sections
      for (const sectionSelector of this.selectors.detailSelectors.features) {
        const section = $(sectionSelector);
        if (!section.length) continue;

        // Look for detail rows/items in this section
        section.find("li, .feature-item, .detail-item, .property-feature").each((i, el) => {
          const text = $(el).text().trim();
          this.extractPropertyDetailsFromText(text, details);
        });
      }

      // Extract images
      details.images = [];
      $('img[src*="huurwoningen"], img[data-src*="huurwoningen"]').each((i, img) => {
        const src = $(img).attr('src') || $(img).attr('data-src');
        if (src && !details.images.includes(src)) {
          details.images.push(src);
        }
      });

      return details;

    } catch (error) {
      const classifiedError = ErrorClassifier.classify(error, this.siteName);
      errorMetrics.recordError(classifiedError, this.siteName);
      
      console.error(`Error fetching Huurwoningen details from ${url}:`, error.message);
      throw classifiedError;
    } finally {
      if (page) {
        await page.close().catch(() => {});
      }
    }
  }

  /**
   * Extract property details from text using patterns
   */
  extractPropertyDetailsFromText(text, details) {
    const lowerText = text.toLowerCase();

    // Size
    const sizeMatch = text.match(/(\d+)\s*m²|woonoppervlakte:\s*(\d+)\s*m²/i);
    if (sizeMatch && !details.size) {
      details.size = `${sizeMatch[1] || sizeMatch[2]} m²`;
    }

    // Rooms
    const roomsMatch = text.match(/(\d+)\s*kamers?|kamers?:\s*(\d+)/i);
    if (roomsMatch && !details.rooms) {
      details.rooms = roomsMatch[1] || roomsMatch[2];
    }

    // Bedrooms
    const bedroomsMatch = text.match(/(\d+)\s*slaapkamers?|slaapkamers?:\s*(\d+)/i);
    if (bedroomsMatch && !details.bedrooms) {
      details.bedrooms = bedroomsMatch[1] || bedroomsMatch[2];
    }

    // Year
    const yearMatch = text.match(/bouwjaar:\s*(\d{4})|(\d{4})\s*gebouwd/i);
    if (yearMatch && !details.year) {
      details.year = yearMatch[1] || yearMatch[2];
    }

    // Energy label
    const energyMatch = text.match(/energielabel:\s*([A-G][\+\-]?)/i);
    if (energyMatch && !details.energyLabel) {
      details.energyLabel = energyMatch[1];
    }

    // Interior
    if (lowerText.includes('gemeubileerd') && !details.interior) {
      details.interior = 'Gemeubileerd';
    } else if (lowerText.includes('gestoffeerd') && !details.interior) {
      details.interior = 'Gestoffeerd';
    } else if (lowerText.includes('kaal') && !details.interior) {
      details.interior = 'Kaal';
    }

    // Available from
    const availableMatch = text.match(/beschikbaar\s*vanaf:\s*([^\n]+)/i);
    if (availableMatch && !details.availableFrom) {
      details.availableFrom = availableMatch[1].trim();
    }

    // Property type
    if (lowerText.includes('appartement') && !details.propertyType) {
      details.propertyType = 'Appartement';
    } else if (lowerText.includes('woning') && !details.propertyType) {
      details.propertyType = 'Woning';
    } else if (lowerText.includes('studio') && !details.propertyType) {
      details.propertyType = 'Studio';
    }

    // Garden
    if (lowerText.includes('tuin') && !details.garden) {
      details.garden = 'Ja';
    }

    // Parking
    if (lowerText.includes('parkeren') && !details.parking) {
      details.parking = 'Ja';
    }
  }

  /**
   * Extract listing data from a single listing element
   */
  async extractListingData($, element) {
    try {
      const titleElement = element.find(this.selectors.title);
      const linkElement = element.find(this.selectors.link);
      const priceElement = element.find(this.selectors.price);
      const locationElement = element.find(this.selectors.location);
      const imageElement = element.find(this.selectors.image);

      const title = titleElement.text().trim();
      const link = linkElement.attr('href');
      const price = priceElement.text().trim();
      const location = locationElement.text().trim();
      const image = imageElement.attr('src') || imageElement.attr('data-src');

      if (!title || !link) {
        return null;
      }

      // Ensure absolute URL
      const fullLink = link.startsWith('http') ? link : `https://www.huurwoningen.nl${link}`;

      return {
        title: this.cleanTitle(title),
        price: this.extractPrice(price),
        location: this.cleanLocation(location),
        link: fullLink,
        image: image ? (image.startsWith('http') ? image : `https:${image}`) : null,
        site: 'huurwoningen',
        dateScraped: new Date()
      };
    } catch (error) {
      console.error('Error extracting Huurwoningen listing:', error.message);
      return null;
    }
  }

  /**
   * Clean extracted title
   */
  cleanTitle(title) {
    if (!title) return null;
    return title.replace(/\s+/g, ' ').trim();
  }

  /**
   * Clean extracted location
   */
  cleanLocation(location) {
    if (!location) return null;
    return location.replace(/\s+/g, ' ').trim();
  }

  /**
   * Enhanced scraping with error handling
   */
  async scrape(retryCount = 0) {
    try {
      await rateLimiter.waitForDelay(this.siteName);
      return await super.scrape(retryCount);
      
    } catch (error) {
      const classifiedError = ErrorClassifier.classify(error, this.siteName);
      errorMetrics.recordError(classifiedError, this.siteName);
      
      const recovery = await ErrorRecovery.handleError(classifiedError, {
        siteName: this.siteName,
        retryCount,
        maxRetries: this.config.retries
      });

      if (recovery.shouldRetry && retryCount < this.config.retries) {
        console.log(`🔄 Applying recovery strategy for ${this.siteName}`);
        
        if (recovery.increaseTimeout) {
          this.config.timeout = Math.min(this.config.timeout * 1.5, 60000);
        }
        
        if (recovery.reduceSpeed) {
          this.config.delayMin *= 1.5;
          this.config.delayMax *= 1.5;
        }

        rateLimiter.recordError(this.siteName, classifiedError);
        return this.scrape(retryCount + 1);
      }

      throw classifiedError;
    }
  }

  /**
   * Validate Huurwoningen-specific listing data
   */
  validateListing(listing) {
    if (!super.validateListing(listing)) {
      return false;
    }

    // Huurwoningen-specific validations
    if (!listing.url || !listing.url.includes('huurwoningen.nl')) {
      return false;
    }

    return true;
  }

  /**
   * Determine if we should fetch detailed information
   */
  shouldFetchDetails() {
    return Math.random() < 0.3; // Fetch details for 30% of listings (slower site)
  }

  /**
   * Get browser instance
   */
  async getBrowserInstance() {
    const { browserPool } = require('../scraperUtils');
    return browserPool.getBrowser();
  }

  /**
   * Override scrape page to add Huurwoningen-specific logic
   */
  async scrapePage(browser, url) {
    try {
      await rateLimiter.waitForDelay(this.siteName);
      return await super.scrapePage(browser, url);
      
    } catch (error) {
      const classifiedError = ErrorClassifier.classify(error, this.siteName);
      rateLimiter.recordError(this.siteName, classifiedError);
      throw classifiedError;
    }
  }
}

// Export both class and factory function
const createHuurwoningenScraper = () => new HuurwoningenScraper();

module.exports = {
  HuurwoningenScraper,
  createHuurwoningenScraper,
  // Export default scraping function for backward compatibility
  scrapeHuurwoningen: async () => {
    const scraper = new HuurwoningenScraper();
    return scraper.scrape();
  }
};
