import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { propertyOwnerService } from '../services/propertyOwnerService';
import { authService } from '../services/authService';
import { getApiBaseUrl } from '../config/api';

export default function TestAPIComponent() {
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, result]);
  };

  const testAPIConfig = () => {
    addResult(`🔧 API Base URL: ${getApiBaseUrl()}`);
    addResult(`🔧 Environment: ${__DEV__ ? 'Development' : 'Production'}`);
  };

  const testLogin = async () => {
    try {
      addResult('🔄 Testing login...');
      
      // Test direct fetch first
      const directResponse = await fetch(`${getApiBaseUrl()}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });
      
      if (directResponse.ok) {
        addResult('✅ Direct fetch successful');
        const directData = await directResponse.json();
        addResult(`Direct response status: ${directData.status}`);
      } else {
        addResult(`❌ Direct fetch failed: ${directResponse.status}`);
      }
      
      // Test auth service
      const response = await authService.login({
        email: '<EMAIL>',
        password: 'password123'
      });
      
      if (response.success) {
        addResult('✅ Auth service login successful');
        addResult(`User role: ${response.data?.user?.role}`);
        addResult(`Is property owner: ${response.data?.user?.propertyOwner?.isPropertyOwner}`);
      } else {
        addResult(`❌ Auth service login failed: ${response.error}`);
      }
    } catch (error: any) {
      addResult(`❌ Login error: ${error.message}`);
    }
  };

  const testProperties = async () => {
    try {
      addResult('🔄 Testing properties...');
      const response = await propertyOwnerService.getProperties();
      
      if (response.success) {
        addResult(`✅ Properties loaded: ${response.data?.length || 0} properties`);
        if (response.data && response.data.length > 0) {
          addResult(`Sample: ${response.data[0].title}`);
        }
      } else {
        addResult(`❌ Properties failed: ${response.error}`);
      }
    } catch (error: any) {
      addResult(`❌ Properties error: ${error.message}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>API Test Component</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={testAPIConfig}>
          <Text style={styles.buttonText}>API Config</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testLogin}>
          <Text style={styles.buttonText}>Test Login</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testProperties}>
          <Text style={styles.buttonText}>Test Properties</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearResults}>
          <Text style={styles.buttonText}>Clear</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>{result}</Text>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    flexWrap: 'wrap',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 5,
    minWidth: 80,
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 5,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  resultText: {
    fontSize: 14,
    marginBottom: 5,
    fontFamily: 'monospace',
  },
});