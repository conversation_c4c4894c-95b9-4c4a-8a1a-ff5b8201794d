import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

interface Property {
  id: string;
  title: string;
  address: string;
  price: number;
  bedrooms: number;
  bathrooms: number;
  size: number;
  images: string[];
  status: 'draft' | 'active' | 'rented' | 'maintenance' | 'inactive';
  occupancyStatus: string;
  applicationsCount: number;
}

interface PropertyCardProps {
  property: Property;
  onPress: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export const PropertyCard: React.FC<PropertyCardProps> = ({
  property,
  onPress,
  onEdit,
  onDelete,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#10b981'; // Green for active/available
      case 'rented':
        return '#4361ee'; // Blue for rented/occupied
      case 'maintenance':
        return '#f59e0b'; // Orange for maintenance
      case 'draft':
        return '#6b7280'; // Gray for draft
      case 'inactive':
        return '#ef4444'; // Red for inactive
      default:
        return '#6b7280';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('nl-NL', {
      style: 'currency',
      currency: 'EUR',
    }).format(price);
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.imageContainer}>
        {property.images && property.images.length > 0 ? (
          <Image source={{ uri: property.images[0] }} style={styles.image} />
        ) : (
          <View style={styles.placeholderImage}>
            <Ionicons name="home" size={48} color="#9ca3af" />
          </View>
        )}
        
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(property.status) }]}>
          <Text style={styles.statusText}>{property.status}</Text>
        </View>
      </View>

      <View style={styles.content}>
        <Text style={styles.title} numberOfLines={1}>
          {property.title}
        </Text>
        
        <Text style={styles.address} numberOfLines={2}>
          {property.address}
        </Text>
        
        <Text style={styles.price}>{formatPrice(property.price)}</Text>
        
        <View style={styles.details}>
          <View style={styles.detailItem}>
            <Ionicons name="bed" size={16} color="#6b7280" />
            <Text style={styles.detailText}>{property.bedrooms} bed</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Ionicons name="water" size={16} color="#6b7280" />
            <Text style={styles.detailText}>{property.bathrooms} bath</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Ionicons name="expand" size={16} color="#6b7280" />
            <Text style={styles.detailText}>{property.size} m²</Text>
          </View>
        </View>

        <View style={styles.footer}>
          <View style={styles.applicationsBadge}>
            <Ionicons name="people" size={14} color="#4361ee" />
            <Text style={styles.applicationsText}>
              {property.applicationsCount} applications
            </Text>
          </View>
          
          <Text style={styles.occupancyText}>{property.occupancyStatus}</Text>
        </View>

        {(onEdit || onDelete) && (
          <View style={styles.actions}>
            {onEdit && (
              <TouchableOpacity onPress={onEdit} style={styles.editButton}>
                <Ionicons name="create" size={20} color="#4361ee" />
              </TouchableOpacity>
            )}
            {onDelete && (
              <TouchableOpacity onPress={onDelete} style={styles.deleteButton}>
                <Ionicons name="trash" size={20} color="#ef4444" />
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: width - 40,
    marginHorizontal: 5,
    marginVertical: 10,
    borderRadius: 12,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  imageContainer: {
    position: 'relative',
    height: 200,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    padding: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 5,
  },
  address: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 10,
  },
  price: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4361ee',
    marginBottom: 10,
  },
  details: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  detailText: {
    fontSize: 12,
    color: '#6b7280',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  applicationsBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    backgroundColor: '#eff6ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  applicationsText: {
    fontSize: 12,
    color: '#4361ee',
  },
  occupancyText: {
    fontSize: 12,
    color: '#6b7280',
  },
  actions: {
    flexDirection: 'row',
    gap: 15,
    marginTop: 10,
  },
  editButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#eff6ff',
  },
  deleteButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#fee2e2',
  },
});
