import { router } from 'expo-router';
import { authService } from './authService';
import { PreferencesValidationService } from './preferencesValidationService';
import { LogService } from './logService';
import { welcomeService } from './welcomeService';

/**
 * Onboarding step definition
 */
export interface OnboardingStep {
  id: string;
  title: string;
  route: string;
  isCompleted?: boolean;
}

/**
 * Navigation service to handle app navigation flows
 */
class NavigationService {
  // Onboarding steps configuration
  private onboardingSteps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome',
      route: '/',
      isCompleted: false
    },
    {
      id: 'auth',
      title: 'Authentication',
      route: '/login',
      isCompleted: false
    },
    {
      id: 'preferences',
      title: 'Preferences',
      route: '/preferences',
      isCompleted: false
    }
  ];
  
  /**
   * Get onboarding steps
   */
  getOnboardingSteps(): OnboardingStep[] {
    return [...this.onboardingSteps];
  }
  
  /**
   * Get current onboarding step
   */
  getCurrentOnboardingStep(route: string): OnboardingStep | undefined {
    return this.onboardingSteps.find(step => step.route === route);
  }
  
  /**
   * Update onboarding step completion status
   */
  updateOnboardingStepStatus(stepId: string, isCompleted: boolean): void {
    const stepIndex = this.onboardingSteps.findIndex(step => step.id === stepId);
    if (stepIndex !== -1) {
      this.onboardingSteps[stepIndex].isCompleted = isCompleted;
    }
  }
  
  /**
   * Navigate to next onboarding step
   */
  navigateToNextOnboardingStep(currentStepId: string): void {
    const currentIndex = this.onboardingSteps.findIndex(step => step.id === currentStepId);
    
    if (currentIndex !== -1 && currentIndex < this.onboardingSteps.length - 1) {
      const nextStep = this.onboardingSteps[currentIndex + 1];
      this.updateOnboardingStepStatus(currentStepId, true);
      
      // Handle route navigation based on the route path
      if (nextStep.route === '/') {
        router.replace('/');
      } else if (nextStep.route === '/login') {
        router.push('/login');
      } else if (nextStep.route === '/preferences') {
        router.push('/preferences');
      } else {
        // For any other routes, try to navigate safely
        try {
          router.push(nextStep.route as any);
        } catch (error) {
          console.error('Navigation error:', error);
          // Fallback to replace if push fails
          router.replace(nextStep.route as any);
        }
      }
    } else {
      // If last step or step not found, go to dashboard
      router.replace('/dashboard');
    }
  }
  
  /**
   * Navigate based on user authentication state
   * - If user is a property owner, go to property owner dashboard
   * - If user is a tenant with preferences, go to tenant dashboard
   * - If user is a tenant without preferences, go to preferences
   * - If user is not authenticated, check if they've seen welcome screen before
   */
  async navigateBasedOnAuthState(): Promise<void> {
    try {
      const isAuthenticated = await authService.isAuthenticated();
      
      if (isAuthenticated) {
        const user = await authService.getCachedUser();
        
        
        // Check if user is a property owner - prioritize role first, then check propertyOwner object
        if (user && (user.role === 'owner' || (user.propertyOwner && user.propertyOwner.isPropertyOwner))) {
          LogService.info('Navigation', `Property owner (role: ${user.role}) authenticated, navigating to property owner dashboard`);
          LogService.navigation('auth', 'property-owner-dashboard', { 
            userId: user.id || user._id, 
            role: user.role 
          });
          
          // Property owner - go to property owner dashboard
          router.replace('/property-owner/dashboard');
          
          // Mark all onboarding steps as completed (property owners don't need tenant preferences)
          this.onboardingSteps.forEach(step => {
            this.updateOnboardingStepStatus(step.id, true);
          });
          return;
        }
        
        // For regular users (tenants), check preferences
        const validationResult = PreferencesValidationService.validatePreferences(user?.preferences);
        LogService.preferenceValidation(validationResult.isValid, validationResult);
        
        if (user && PreferencesValidationService.hasMinimumRequiredPreferences(user.preferences)) {
          LogService.info('Navigation', 'User has valid preferences, navigating to dashboard');
          LogService.navigation('auth', 'dashboard', { userId: user.id || user._id });
          
          // User has valid preferences, go to dashboard
          router.replace('/dashboard');
          
          // Mark all onboarding steps as completed
          this.onboardingSteps.forEach(step => {
            this.updateOnboardingStepStatus(step.id, true);
          });
        } else {
          LogService.warn('Navigation', 'User has invalid or incomplete preferences, redirecting to preferences setup');
          LogService.navigation('auth', 'preferences', { userId: user?.id || user?._id, missingFields: validationResult.missingFields });
          
          // User needs to set preferences
          router.replace('/preferences');
          
          // Mark welcome and auth steps as completed
          this.updateOnboardingStepStatus('welcome', true);
          this.updateOnboardingStepStatus('auth', true);
        }
      } else {
        // Not authenticated - check if user has seen welcome screen before
        const hasSeenWelcome = await welcomeService.hasSeenWelcome();
        
        if (hasSeenWelcome) {
          LogService.info('Navigation', 'User not authenticated but has seen welcome screen, going to login');
          // Skip welcome screen for returning users
          router.replace('/login');
        } else {
          LogService.info('Navigation', 'User not authenticated and first time, showing welcome screen');
          // First time user, show welcome screen
          router.replace('/');
        }
        
        // Reset all onboarding steps
        this.onboardingSteps.forEach(step => {
          this.updateOnboardingStepStatus(step.id, false);
        });
      }
    } catch (error) {
      console.error('Navigation error:', error);
      // Default to welcome screen on error
      router.replace('/');
    }
  }
  
  /**
   * Navigate after successful authentication
   * - If user is a property owner, go to property owner dashboard
   * - If user has preferences, go to tenant dashboard
   * - If user doesn't have preferences, go to preferences
   */
  async navigateAfterAuth(): Promise<void> {
    try {
      const user = await authService.getCachedUser();
      
      // Enhanced debug logging for navigation decision
      console.log('==========================================');
      console.log('NAVIGATION AFTER AUTH - DETAILED USER DATA');
      console.log('==========================================');
      console.log('User ID:', user?._id || user?.id);
      console.log('User Email:', user?.email);
      console.log('User Role:', user?.role);
      console.log('Is Property Owner Object:', JSON.stringify(user?.propertyOwner));
      console.log('Is Property Owner Flag:', !!user?.propertyOwner?.isPropertyOwner);
      console.log('Has Preferences:', !!user?.preferences);
      console.log('Preferences Object:', JSON.stringify(user?.preferences));
      console.log('Full User Object:', JSON.stringify(user));
      console.log('==========================================');
      
      // Mark welcome and auth steps as completed
      this.updateOnboardingStepStatus('welcome', true);
      this.updateOnboardingStepStatus('auth', true);
      
      // Check if user is a property owner - prioritize role first, then check propertyOwner object
      const isOwnerRole = user?.role === 'owner';
      const hasPropertyOwnerFlag = user?.propertyOwner && user.propertyOwner.isPropertyOwner;
      
      console.log('Navigation Decision Factors:');
      console.log('- Is Owner Role:', isOwnerRole);
      console.log('- Has Property Owner Flag:', hasPropertyOwnerFlag);
      
      if (user && (isOwnerRole || hasPropertyOwnerFlag)) {
        console.log('DECISION: User is a property owner, navigating to property owner dashboard');
        LogService.info('Navigation', `Property owner (role: ${user.role}) authenticated, navigating to property owner dashboard`);
        LogService.navigation('login', 'property-owner-dashboard', { 
          userId: user.id || user._id, 
          role: user.role,
          isPropertyOwner: !!user.propertyOwner?.isPropertyOwner 
        });
        
        // Property owner - go to property owner dashboard
        console.log('Replacing route with: /property-owner/dashboard');
        router.replace('/property-owner/dashboard');
        
        // Mark preferences step as completed (property owners don't need tenant preferences)
        this.updateOnboardingStepStatus('preferences', true);
        return;
      }
      
      console.log('DECISION: User is NOT a property owner, continuing with tenant flow');
      
      // For regular users (tenants), check preferences
      const validationResult = PreferencesValidationService.validatePreferences(user?.preferences);
      LogService.preferenceValidation(validationResult.isValid, validationResult);
      
      if (user && PreferencesValidationService.hasMinimumRequiredPreferences(user.preferences)) {
        LogService.info('Navigation', 'User has valid preferences after auth, navigating to dashboard');
        LogService.navigation('login', 'dashboard', { userId: user.id });
        
        // User has valid preferences, go to dashboard
        router.replace('/dashboard');
        
        // Mark preferences step as completed
        this.updateOnboardingStepStatus('preferences', true);
      } else {
        LogService.warn('Navigation', 'User has invalid or incomplete preferences after auth, redirecting to preferences setup');
        LogService.navigation('login', 'preferences', { userId: user?.id, missingFields: validationResult.missingFields });
        
        // User needs to set preferences
        router.replace('/preferences');
      }
    } catch (error) {
      LogService.error('Navigation', 'Post-auth navigation error', error);
      // Default to preferences on error
      router.replace('/preferences');
    }
  }
  
  /**
   * Navigate after successful preferences setup
   * @param preferencesComplete Optional flag to indicate if preferences are complete
   */
  async navigateAfterPreferences(preferencesComplete: boolean = false): Promise<void> {
    try {
      // If preferences completion status is not explicitly provided, validate them
      if (!preferencesComplete) {
        const user = await authService.getCachedUser();
        
        // Validate preferences
        const validationResult = PreferencesValidationService.validatePreferences(user?.preferences);
        LogService.preferenceValidation(validationResult.isValid, validationResult);
        
        // Fix TypeScript error: Type 'boolean | null' is not assignable to type 'boolean'
        preferencesComplete = !!(user && 
                             PreferencesValidationService.hasMinimumRequiredPreferences(user.preferences));
      }
      
      if (preferencesComplete) {
        LogService.info('Navigation', 'Preferences setup complete, navigating to dashboard');
        LogService.navigation('preferences', 'dashboard', { preferencesComplete });
        
        // Mark preferences step as completed
        this.updateOnboardingStepStatus('preferences', true);
        
        // Navigate to dashboard
        router.replace('/dashboard');
      } else {
        LogService.warn('Navigation', 'Preferences setup incomplete, staying on preferences page');
        // Don't navigate away from preferences page
      }
    } catch (error) {
      LogService.error('Navigation', 'Error navigating after preferences', error);
      // Stay on preferences page if there's an error
    }
  }
  
  /**
   * Navigate to login screen
   */
  navigateToLogin(): void {
    // Mark welcome step as completed
    this.updateOnboardingStepStatus('welcome', true);
    
    router.push('/login');
  }
  
  /**
   * Navigate to welcome screen
   */
  navigateToWelcome(): void {
    router.replace('/');
  }
  
  /**
   * Skip onboarding and go to dashboard
   */
  skipOnboarding(): void {
    // Mark all onboarding steps as completed
    this.onboardingSteps.forEach(step => {
      this.updateOnboardingStepStatus(step.id, true);
    });
    
    // Navigate to dashboard
    router.replace('/dashboard');
  }
  
  /**
   * Check if onboarding is complete
   */
  isOnboardingComplete(): boolean {
    return this.onboardingSteps.every(step => step.isCompleted);
  }
}

// Export singleton instance
export const navigationService = new NavigationService();
export default navigationService;