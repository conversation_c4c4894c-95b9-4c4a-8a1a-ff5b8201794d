# Scraper Auto-Application Integration

This document describes the integration between the existing scraper system and the auto-application feature for Funda listings.

## Overview

The scraper auto-application integration automatically processes new listings from the scraper and triggers auto-applications for users who have matching criteria. This enables immediate application opportunities when new properties become available.

## Architecture

### Components

1. **ScraperAutoApplicationIntegration Service** (`src/services/scraperAutoApplicationIntegration.js`)
   - Main integration service that processes new listings
   - Calculates quality scores for listings
   - Matches listings against user criteria
   - Triggers auto-applications for matching users

2. **Modified Scraper Service** (`src/services/scraper.js`)
   - Enhanced to call the integration service after scraping each site
   - Processes Funda, Pararius, and Huurwoningen listings

3. **Integration Tests** 
   - Unit tests: `src/tests/services/scraperAutoApplicationIntegration.test.js`
   - Integration tests: `src/tests/integration/scraperAutoApplicationIntegration.integration.test.js`

## Features

### Real-time Listing Processing
- Processes new listings immediately after scraping
- Identifies users with matching criteria
- Triggers auto-applications within minutes of listing discovery

### Listing Quality Scoring
- Calculates quality scores based on:
  - Price availability and format (20%)
  - Description quality (15%)
  - Property details completeness (20%)
  - Images availability (10%)
  - Location specificity (5%)
  - Recency bonus (up to 10%)
  - Property type preference (5%)

### Duplicate Detection
- Prevents multiple applications to the same property
- Uses URL as primary identifier
- Falls back to title + location matching

### Criteria Matching
- Price range filtering
- Room count requirements
- Property type preferences
- Location filtering
- Keyword inclusion/exclusion
- Size requirements
- Furnished preference matching

### Performance Optimization
- Batch processing to avoid system overload
- Quality score caching
- Processed listings tracking
- Configurable processing limits

## Configuration

### Quality Score Thresholds
- Minimum quality score: 0.6 (configurable)
- High-quality listings (>0.8) get priority processing

### Processing Limits
- Maximum concurrent processing: 5 listings
- Batch processing with 1-second delays between batches

### Cache Settings
- Quality score cache expiry: 24 hours
- Processed listings cache (in-memory)

## Usage

### Automatic Integration
The integration runs automatically when the scraper agent is active:

```javascript
const { startAgent } = require('./src/services/scraper');

// Start the scraper agent - auto-application integration runs automatically
await startAgent();
```

### Manual Processing
You can also process listings manually:

```javascript
const scraperAutoApplicationIntegration = require('./src/services/scraperAutoApplicationIntegration');

const mockListings = [
  {
    title: 'Beautiful Apartment',
    price: '€ 1.500 per maand',
    location: 'Utrecht',
    url: 'https://www.funda.nl/huur/utrecht/appartement-test/',
    // ... other listing properties
  }
];

const result = await scraperAutoApplicationIntegration.processNewListings(mockListings, 'funda.nl');
console.log(`Triggered ${result.autoApplicationTriggered} auto-applications`);
```

### Statistics and Monitoring
Get integration statistics:

```javascript
const stats = scraperAutoApplicationIntegration.getStatistics();
console.log('Integration Statistics:', stats);
```

Clear caches for maintenance:

```javascript
scraperAutoApplicationIntegration.clearCaches();
```

## Testing

### Running Unit Tests
```bash
npm test -- --testPathPattern="scraperAutoApplicationIntegration.test.js"
```

### Running Integration Tests
```bash
npm test -- --testPathPattern="scraperAutoApplicationIntegration.integration.test.js"
```

### Manual Testing
```bash
node test-scraper-integration.js
```

## Error Handling

The integration includes comprehensive error handling:

- **Network Errors**: Graceful handling of database connection issues
- **Data Validation**: Validates listing data before processing
- **Processing Errors**: Continues processing other listings if one fails
- **Auto-Application Errors**: Logs errors but doesn't fail the scraping process

## Monitoring and Logging

All integration activities are logged using the application logger:

- Info level: Processing summaries and successful operations
- Debug level: Detailed matching and scoring information
- Error level: Failed operations and exceptions

## Performance Considerations

### Scalability
- Processes listings in configurable batches
- Implements rate limiting to avoid overwhelming the system
- Uses caching to reduce redundant calculations

### Resource Management
- Limits concurrent processing
- Cleans up processed listings cache periodically
- Implements timeout handling for long-running operations

## Requirements Fulfilled

This integration fulfills the following requirements from the specification:

- **Requirement 1.2**: Automatic application within 5 minutes of discovery
- **Requirement 7.1**: Learning from successful applications and improving over time

## Future Enhancements

Potential improvements for future versions:

1. **Machine Learning Integration**: Use ML models for better quality scoring
2. **Advanced Filtering**: More sophisticated criteria matching algorithms
3. **Performance Metrics**: Detailed analytics on integration performance
4. **Real-time Notifications**: WebSocket integration for real-time updates
5. **A/B Testing**: Test different scoring algorithms and matching strategies

## Troubleshooting

### Common Issues

1. **No Auto-Applications Triggered**
   - Check if users have auto-application enabled
   - Verify user criteria match the listings
   - Check daily application limits

2. **Low Quality Scores**
   - Review quality scoring algorithm
   - Check if listings have complete data
   - Verify image and description availability

3. **Performance Issues**
   - Reduce batch size or concurrent processing limits
   - Clear caches if memory usage is high
   - Check database connection performance

### Debug Mode
Enable debug logging to troubleshoot issues:

```javascript
// Set log level to debug in your configuration
process.env.LOG_LEVEL = 'debug';
```

## API Integration

The integration is automatically included in the scraper service exports:

```javascript
const { scraperAutoApplicationIntegration } = require('./src/services/scraper');

// Access integration methods
const stats = scraperAutoApplicationIntegration.getStatistics();
```

This integration provides a seamless bridge between the scraping system and auto-application features, enabling users to automatically apply to properties as soon as they become available on the market.