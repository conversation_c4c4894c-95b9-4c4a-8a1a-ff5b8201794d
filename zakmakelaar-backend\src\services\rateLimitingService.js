const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const ApplicationQueue = require('../models/ApplicationQueue');
const ApplicationResult = require('../models/ApplicationResult');
const { loggers } = require('./logger');

/**
 * RateLimitingService - Comprehensive rate limiting and compliance system
 * 
 * This service provides:
 * - Daily application limits with reset logic
 * - Intelligent scheduling to spread applications throughout the day
 * - Detection and handling of Funda rate limiting responses
 * - Automatic pause/resume functionality based on platform feedback
 * - Compliance monitoring and reporting system
 */
class RateLimitingService {
  constructor() {
    this.globalLimits = {
      maxApplicationsPerHour: 50,
      maxApplicationsPerDay: 500,
      maxConcurrentApplications: 5
    };
    
    this.userLimits = {
      maxApplicationsPerHour: 5,
      maxApplicationsPerDay: 20,
      maxConcurrentApplications: 2
    };

    // Rate limiting counters
    this.globalCounters = {
      hourly: 0,
      daily: 0,
      concurrent: 0,
      lastHourReset: new Date(),
      lastDayReset: new Date()
    };

    this.userCounters = new Map(); // userId -> counters
    this.pausedUsers = new Map(); // userId -> pause info
    this.complianceMetrics = {
      totalApplications: 0,
      blockedApplications: 0,
      rateLimitHits: 0,
      captchaEncounters: 0,
      fundaRateLimitDetections: 0,
      lastComplianceCheck: new Date()
    };

    // Funda rate limiting detection patterns
    this.fundaRateLimitPatterns = [
      /too many requests/i,
      /rate limit exceeded/i,
      /please wait/i,
      /temporarily blocked/i,
      /suspicious activity/i,
      /bot detected/i
    ];

    // Initialize reset timers
    this.initializeResetTimers();
  }

  /**
   * Initialize reset timers for rate limiting counters
   */
  initializeResetTimers() {
    // Reset hourly counters every hour
    setInterval(() => {
      this.resetHourlyCounters();
    }, 60 * 60 * 1000);

    // Reset daily counters at midnight
    const now = new Date();
    const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    const msUntilMidnight = tomorrow.getTime() - now.getTime();

    setTimeout(() => {
      this.resetDailyCounters();
      // Then reset daily every 24 hours
      setInterval(() => {
        this.resetDailyCounters();
      }, 24 * 60 * 60 * 1000);
    }, msUntilMidnight);

    loggers.app.info('Rate limiting reset timers initialized');
  }

  /**
   * Check if an application can be processed based on rate limits
   * @param {string} userId - User ID
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Rate limit check result
   */
  async checkRateLimit(userId, options = {}) {
    try {
      const { priority = 'normal', bypassUserLimits = false } = options;

      // Check if user is paused
      const pauseInfo = this.pausedUsers.get(userId);
      if (pauseInfo && pauseInfo.pausedUntil > new Date()) {
        return {
          allowed: false,
          reason: 'user_paused',
          message: `User is paused until ${pauseInfo.pausedUntil}`,
          pauseReason: pauseInfo.reason,
          retryAfter: pauseInfo.pausedUntil
        };
      }

      // Check global limits
      const globalCheck = this.checkGlobalLimits();
      if (!globalCheck.allowed) {
        return globalCheck;
      }

      // Check user limits (unless bypassed)
      if (!bypassUserLimits) {
        const userCheck = this.checkUserLimits(userId);
        if (!userCheck.allowed) {
          return userCheck;
        }
      }

      // Check intelligent scheduling
      const schedulingCheck = await this.checkIntelligentScheduling(userId, priority);
      if (!schedulingCheck.allowed) {
        return schedulingCheck;
      }

      return {
        allowed: true,
        message: 'Application can be processed',
        scheduledDelay: schedulingCheck.recommendedDelay || 0
      };

    } catch (error) {
      loggers.app.error('Error checking rate limits:', error);
      return {
        allowed: false,
        reason: 'error',
        message: 'Rate limit check failed'
      };
    }
  }

  /**
   * Check global rate limits
   * @returns {Object} Global rate limit check result
   */
  checkGlobalLimits() {
    // Reset counters if needed
    this.resetCountersIfNeeded();

    // Check hourly limit
    if (this.globalCounters.hourly >= this.globalLimits.maxApplicationsPerHour) {
      const nextReset = new Date(this.globalCounters.lastHourReset.getTime() + 60 * 60 * 1000);
      return {
        allowed: false,
        reason: 'global_hourly_limit',
        message: 'Global hourly application limit reached',
        retryAfter: nextReset,
        current: this.globalCounters.hourly,
        limit: this.globalLimits.maxApplicationsPerHour
      };
    }

    // Check daily limit
    if (this.globalCounters.daily >= this.globalLimits.maxApplicationsPerDay) {
      const nextReset = new Date(this.globalCounters.lastDayReset.getTime() + 24 * 60 * 60 * 1000);
      return {
        allowed: false,
        reason: 'global_daily_limit',
        message: 'Global daily application limit reached',
        retryAfter: nextReset,
        current: this.globalCounters.daily,
        limit: this.globalLimits.maxApplicationsPerDay
      };
    }

    // Check concurrent limit
    if (this.globalCounters.concurrent >= this.globalLimits.maxConcurrentApplications) {
      return {
        allowed: false,
        reason: 'global_concurrent_limit',
        message: 'Global concurrent application limit reached',
        current: this.globalCounters.concurrent,
        limit: this.globalLimits.maxConcurrentApplications
      };
    }

    return { allowed: true };
  }

  /**
   * Check user-specific rate limits
   * @param {string} userId - User ID
   * @returns {Object} User rate limit check result
   */
  checkUserLimits(userId) {
    const userCounters = this.getUserCounters(userId);

    // Check hourly limit
    if (userCounters.hourly >= this.userLimits.maxApplicationsPerHour) {
      const nextReset = new Date(userCounters.lastHourReset.getTime() + 60 * 60 * 1000);
      return {
        allowed: false,
        reason: 'user_hourly_limit',
        message: 'User hourly application limit reached',
        retryAfter: nextReset,
        current: userCounters.hourly,
        limit: this.userLimits.maxApplicationsPerHour
      };
    }

    // Check daily limit
    if (userCounters.daily >= this.userLimits.maxApplicationsPerDay) {
      const nextReset = new Date(userCounters.lastDayReset.getTime() + 24 * 60 * 60 * 1000);
      return {
        allowed: false,
        reason: 'user_daily_limit',
        message: 'User daily application limit reached',
        retryAfter: nextReset,
        current: userCounters.daily,
        limit: this.userLimits.maxApplicationsPerDay
      };
    }

    // Check concurrent limit
    if (userCounters.concurrent >= this.userLimits.maxConcurrentApplications) {
      return {
        allowed: false,
        reason: 'user_concurrent_limit',
        message: 'User concurrent application limit reached',
        current: userCounters.concurrent,
        limit: this.userLimits.maxConcurrentApplications
      };
    }

    return { allowed: true };
  }

  /**
   * Check intelligent scheduling to spread applications throughout the day
   * @param {string} userId - User ID
   * @param {string} priority - Application priority
   * @returns {Promise<Object>} Scheduling check result
   */
  async checkIntelligentScheduling(userId, priority = 'normal') {
    try {
      // Get user's auto-application settings
      const userSettings = await AutoApplicationSettings.findByUserId(userId);
      if (!userSettings) {
        return { allowed: true };
      }

      const maxDailyApplications = userSettings.settings.maxApplicationsPerDay || this.userLimits.maxApplicationsPerDay;
      
      // Get today's applications for this user
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);
      
      const todayApplications = await ApplicationQueue.find({
        userId,
        createdAt: { $gte: todayStart },
        status: { $in: ['pending', 'processing', 'completed'] }
      }).sort({ scheduledAt: 1 });

      const applicationsToday = todayApplications.length;

      // If we've reached the daily limit, don't allow more
      if (applicationsToday >= maxDailyApplications) {
        return {
          allowed: false,
          reason: 'daily_limit_reached',
          message: 'Daily application limit reached for user'
        };
      }

      // Calculate optimal spacing throughout the day
      const remainingApplications = maxDailyApplications - applicationsToday;
      const hoursRemainingToday = this.getHoursRemainingToday();
      
      // Calculate recommended delay based on intelligent scheduling
      let recommendedDelay = 0;
      
      if (priority === 'high') {
        // High priority applications get minimal delay
        recommendedDelay = Math.random() * 5 * 60 * 1000; // 0-5 minutes
      } else if (priority === 'low') {
        // Low priority applications get longer delay
        recommendedDelay = (Math.random() * 60 + 30) * 60 * 1000; // 30-90 minutes
      } else {
        // Normal priority - spread evenly throughout remaining day
        if (remainingApplications > 0 && hoursRemainingToday > 0) {
          const optimalSpacing = (hoursRemainingToday * 60 * 60 * 1000) / remainingApplications;
          const variation = optimalSpacing * 0.3; // ±30% variation
          recommendedDelay = Math.max(
            2 * 60 * 1000, // Minimum 2 minutes
            optimalSpacing + (Math.random() - 0.5) * variation
          );
        } else {
          recommendedDelay = 5 * 60 * 1000; // Default 5 minutes
        }
      }

      // Check if there are recent applications that are too close
      const recentApplications = todayApplications.filter(app => {
        const timeDiff = new Date() - new Date(app.scheduledAt);
        return timeDiff < 10 * 60 * 1000; // Within last 10 minutes
      });

      if (recentApplications.length > 0 && priority !== 'high') {
        // Add additional delay to avoid clustering
        recommendedDelay = Math.max(recommendedDelay, 10 * 60 * 1000); // At least 10 minutes
      }

      return {
        allowed: true,
        recommendedDelay: Math.round(recommendedDelay),
        applicationsToday,
        remainingToday: remainingApplications,
        optimalSpacing: remainingApplications > 0 && hoursRemainingToday > 0 ? 
          Math.round((hoursRemainingToday * 60 * 60 * 1000) / remainingApplications) : 0
      };

    } catch (error) {
      loggers.app.error('Error in intelligent scheduling check:', error);
      return { allowed: true, recommendedDelay: 5 * 60 * 1000 }; // Default 5 minute delay
    }
  }

  /**
   * Record an application attempt and update counters
   * @param {string} userId - User ID
   * @param {Object} applicationData - Application data
   * @returns {Promise<void>}
   */
  async recordApplicationAttempt(userId, applicationData = {}) {
    try {
      // Update global counters
      this.globalCounters.hourly++;
      this.globalCounters.daily++;
      this.globalCounters.concurrent++;

      // Update user counters
      const userCounters = this.getUserCounters(userId);
      userCounters.hourly++;
      userCounters.daily++;
      userCounters.concurrent++;

      // Update compliance metrics
      this.complianceMetrics.totalApplications++;

      loggers.app.info(`Recorded application attempt for user ${userId}`, {
        globalHourly: this.globalCounters.hourly,
        globalDaily: this.globalCounters.daily,
        userHourly: userCounters.hourly,
        userDaily: userCounters.daily
      });

    } catch (error) {
      loggers.app.error('Error recording application attempt:', error);
    }
  }

  /**
   * Record application completion and update counters
   * @param {string} userId - User ID
   * @param {boolean} success - Whether the application was successful
   * @returns {Promise<void>}
   */
  async recordApplicationCompletion(userId, success = true) {
    try {
      // Decrease concurrent counters
      this.globalCounters.concurrent = Math.max(0, this.globalCounters.concurrent - 1);
      
      const userCounters = this.getUserCounters(userId);
      userCounters.concurrent = Math.max(0, userCounters.concurrent - 1);

      loggers.app.info(`Recorded application completion for user ${userId}`, {
        success,
        globalConcurrent: this.globalCounters.concurrent,
        userConcurrent: userCounters.concurrent
      });

    } catch (error) {
      loggers.app.error('Error recording application completion:', error);
    }
  }

  /**
   * Detect Funda rate limiting responses
   * @param {string} responseText - Response text from Funda
   * @param {Object} responseHeaders - Response headers
   * @param {number} statusCode - HTTP status code
   * @returns {Object} Detection result
   */
  detectFundaRateLimit(responseText, responseHeaders = {}, statusCode = 200) {
    try {
      const detection = {
        isRateLimit: false,
        severity: 'none',
        reason: null,
        recommendedAction: null,
        pauseDuration: 0
      };

      // Check HTTP status codes
      if (statusCode === 429) {
        detection.isRateLimit = true;
        detection.severity = 'high';
        detection.reason = 'HTTP 429 Too Many Requests';
        detection.recommendedAction = 'pause_user';
        detection.pauseDuration = 60 * 60 * 1000; // 1 hour
      } else if (statusCode === 503) {
        detection.isRateLimit = true;
        detection.severity = 'medium';
        detection.reason = 'HTTP 503 Service Unavailable';
        detection.recommendedAction = 'pause_user';
        detection.pauseDuration = 30 * 60 * 1000; // 30 minutes
      }

      // Check response headers
      if (responseHeaders['retry-after']) {
        detection.isRateLimit = true;
        detection.severity = 'high';
        detection.reason = 'Retry-After header present';
        detection.recommendedAction = 'pause_user';
        detection.pauseDuration = parseInt(responseHeaders['retry-after']) * 1000;
      }

      // Check response text for rate limiting patterns
      if (responseText) {
        for (const pattern of this.fundaRateLimitPatterns) {
          if (pattern.test(responseText)) {
            detection.isRateLimit = true;
            detection.severity = 'medium';
            detection.reason = `Pattern match: ${pattern.source}`;
            detection.recommendedAction = 'pause_user';
            detection.pauseDuration = 30 * 60 * 1000; // 30 minutes
            break;
          }
        }
      }

      // Check for CAPTCHA indicators
      if (responseText && (
        responseText.includes('captcha') ||
        responseText.includes('recaptcha') ||
        responseText.includes('hcaptcha') ||
        responseText.includes('verification')
      )) {
        detection.isRateLimit = true;
        detection.severity = 'high';
        detection.reason = 'CAPTCHA detected';
        detection.recommendedAction = 'pause_user_manual';
        detection.pauseDuration = 2 * 60 * 60 * 1000; // 2 hours
        this.complianceMetrics.captchaEncounters++;
      }

      if (detection.isRateLimit) {
        this.complianceMetrics.fundaRateLimitDetections++;
        loggers.app.warn('Funda rate limit detected', detection);
      }

      return detection;

    } catch (error) {
      loggers.app.error('Error detecting Funda rate limit:', error);
      return {
        isRateLimit: false,
        severity: 'none',
        reason: 'Detection error',
        recommendedAction: null,
        pauseDuration: 0
      };
    }
  }

  /**
   * Pause a user's auto-application based on platform feedback
   * @param {string} userId - User ID
   * @param {string} reason - Reason for pause
   * @param {number} duration - Pause duration in milliseconds
   * @param {boolean} requiresManualResume - Whether manual resume is required
   * @returns {Promise<void>}
   */
  async pauseUser(userId, reason, duration = 60 * 60 * 1000, requiresManualResume = false) {
    try {
      const pausedUntil = requiresManualResume ? 
        new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) : // 1 year (effectively manual)
        new Date(Date.now() + duration);

      this.pausedUsers.set(userId, {
        reason,
        pausedAt: new Date(),
        pausedUntil,
        requiresManualResume,
        duration
      });

      // Update user settings
      const userSettings = await AutoApplicationSettings.findByUserId(userId);
      if (userSettings) {
        userSettings.status.pausedReason = reason;
        userSettings.status.pausedUntil = pausedUntil;
        userSettings.status.isActive = false;
        await userSettings.save();
      }

      // Cancel pending applications for this user
      await ApplicationQueue.updateMany(
        { userId, status: 'pending' },
        { 
          status: 'cancelled',
          'metadata.cancelReason': `User paused: ${reason}`
        }
      );

      loggers.app.warn(`User ${userId} paused for ${duration}ms: ${reason}`, {
        pausedUntil,
        requiresManualResume
      });

    } catch (error) {
      loggers.app.error(`Error pausing user ${userId}:`, error);
    }
  }

  /**
   * Resume a user's auto-application
   * @param {string} userId - User ID
   * @param {string} resumeReason - Reason for resume
   * @returns {Promise<void>}
   */
  async resumeUser(userId, resumeReason = 'Manual resume') {
    try {
      this.pausedUsers.delete(userId);

      // Update user settings
      const userSettings = await AutoApplicationSettings.findByUserId(userId);
      if (userSettings) {
        userSettings.status.pausedReason = undefined;
        userSettings.status.pausedUntil = undefined;
        userSettings.status.isActive = userSettings.enabled;
        await userSettings.save();
      }

      loggers.app.info(`User ${userId} resumed: ${resumeReason}`);

    } catch (error) {
      loggers.app.error(`Error resuming user ${userId}:`, error);
    }
  }

  /**
   * Get compliance report
   * @param {Object} options - Report options
   * @returns {Promise<Object>} Compliance report
   */
  async getComplianceReport(options = {}) {
    try {
      const { startDate, endDate, userId } = options;
      
      // Build query for applications
      const query = {};
      if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) query.createdAt.$gte = new Date(startDate);
        if (endDate) query.createdAt.$lte = new Date(endDate);
      }
      if (userId) query.userId = userId;

      // Get application statistics
      const applicationStats = await ApplicationResult.aggregate([
        { $match: query },
        {
          $group: {
            _id: null,
            totalApplications: { $sum: 1 },
            successfulApplications: { $sum: { $cond: [{ $eq: ['$status', 'submitted'] }, 1, 0] } },
            failedApplications: { $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] } },
            blockedApplications: { $sum: { $cond: [{ $eq: ['$status', 'blocked'] }, 1, 0] } },
            captchaApplications: { $sum: { $cond: [{ $eq: ['$status', 'captcha_required'] }, 1, 0] } },
            avgProcessingTime: { $avg: '$metrics.processingTime' }
          }
        }
      ]);

      const stats = applicationStats[0] || {
        totalApplications: 0,
        successfulApplications: 0,
        failedApplications: 0,
        blockedApplications: 0,
        captchaApplications: 0,
        avgProcessingTime: 0
      };

      // Get rate limiting statistics
      const rateLimitStats = {
        globalLimits: this.globalLimits,
        userLimits: this.userLimits,
        currentGlobalCounters: this.globalCounters,
        activeUserCount: this.userCounters.size,
        pausedUserCount: this.pausedUsers.size
      };

      // Get paused users information
      const pausedUsers = Array.from(this.pausedUsers.entries()).map(([userId, pauseInfo]) => ({
        userId,
        reason: pauseInfo.reason,
        pausedAt: pauseInfo.pausedAt,
        pausedUntil: pauseInfo.pausedUntil,
        requiresManualResume: pauseInfo.requiresManualResume
      }));

      // Calculate compliance score
      const complianceScore = this.calculateComplianceScore(stats);

      return {
        reportGeneratedAt: new Date(),
        period: {
          startDate: startDate || 'All time',
          endDate: endDate || 'Present'
        },
        applicationStatistics: stats,
        rateLimitingStatistics: rateLimitStats,
        complianceMetrics: {
          ...this.complianceMetrics,
          complianceScore,
          successRate: stats.totalApplications > 0 ? 
            (stats.successfulApplications / stats.totalApplications * 100).toFixed(2) + '%' : '0%',
          blockRate: stats.totalApplications > 0 ? 
            (stats.blockedApplications / stats.totalApplications * 100).toFixed(2) + '%' : '0%'
        },
        pausedUsers,
        recommendations: this.generateComplianceRecommendations(stats, complianceScore)
      };

    } catch (error) {
      loggers.app.error('Error generating compliance report:', error);
      throw error;
    }
  }

  /**
   * Calculate compliance score based on application statistics
   * @param {Object} stats - Application statistics
   * @returns {number} Compliance score (0-100)
   */
  calculateComplianceScore(stats) {
    let score = 100;

    // Deduct points for high block rate
    if (stats.totalApplications > 0) {
      const blockRate = stats.blockedApplications / stats.totalApplications;
      score -= blockRate * 50; // Up to 50 points deduction

      // Deduct points for CAPTCHA encounters
      const captchaRate = stats.captchaApplications / stats.totalApplications;
      score -= captchaRate * 30; // Up to 30 points deduction

      // Deduct points for high failure rate
      const failureRate = stats.failedApplications / stats.totalApplications;
      score -= failureRate * 20; // Up to 20 points deduction
    }

    // Deduct points for rate limit hits
    score -= Math.min(this.complianceMetrics.rateLimitHits * 2, 20);

    // Deduct points for Funda rate limit detections
    score -= Math.min(this.complianceMetrics.fundaRateLimitDetections * 5, 30);

    return Math.max(0, Math.round(score));
  }

  /**
   * Generate compliance recommendations
   * @param {Object} stats - Application statistics
   * @param {number} complianceScore - Compliance score
   * @returns {Array} Array of recommendations
   */
  generateComplianceRecommendations(stats, complianceScore) {
    const recommendations = [];

    if (complianceScore < 70) {
      recommendations.push({
        priority: 'high',
        category: 'compliance',
        message: 'Compliance score is below acceptable threshold',
        action: 'Review and adjust rate limiting settings'
      });
    }

    if (stats.totalApplications > 0) {
      const blockRate = stats.blockedApplications / stats.totalApplications;
      if (blockRate > 0.1) {
        recommendations.push({
          priority: 'high',
          category: 'blocking',
          message: `High block rate detected: ${(blockRate * 100).toFixed(1)}%`,
          action: 'Increase delays between applications and review anti-detection measures'
        });
      }

      const captchaRate = stats.captchaApplications / stats.totalApplications;
      if (captchaRate > 0.05) {
        recommendations.push({
          priority: 'medium',
          category: 'captcha',
          message: `Frequent CAPTCHA encounters: ${(captchaRate * 100).toFixed(1)}%`,
          action: 'Improve browser fingerprinting and human-like behavior simulation'
        });
      }
    }

    if (this.complianceMetrics.fundaRateLimitDetections > 10) {
      recommendations.push({
        priority: 'high',
        category: 'rate_limiting',
        message: 'Multiple Funda rate limit detections',
        action: 'Reduce application frequency and implement longer delays'
      });
    }

    if (this.pausedUsers.size > 5) {
      recommendations.push({
        priority: 'medium',
        category: 'user_management',
        message: `${this.pausedUsers.size} users currently paused`,
        action: 'Review pause reasons and consider system-wide adjustments'
      });
    }

    return recommendations;
  }

  /**
   * Get or create user counters
   * @param {string} userId - User ID
   * @returns {Object} User counters
   */
  getUserCounters(userId) {
    if (!this.userCounters.has(userId)) {
      const now = new Date();
      this.userCounters.set(userId, {
        hourly: 0,
        daily: 0,
        concurrent: 0,
        lastHourReset: new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours()),
        lastDayReset: new Date(now.getFullYear(), now.getMonth(), now.getDate())
      });
    }
    return this.userCounters.get(userId);
  }

  /**
   * Reset hourly counters
   */
  resetHourlyCounters() {
    const now = new Date();
    
    // Reset global hourly counter
    this.globalCounters.hourly = 0;
    this.globalCounters.lastHourReset = now;

    // Reset user hourly counters
    for (const [userId, counters] of this.userCounters.entries()) {
      counters.hourly = 0;
      counters.lastHourReset = now;
    }

    loggers.app.info('Hourly rate limit counters reset');
  }

  /**
   * Reset daily counters
   */
  resetDailyCounters() {
    const now = new Date();
    
    // Reset global daily counter
    this.globalCounters.daily = 0;
    this.globalCounters.lastDayReset = now;

    // Reset user daily counters
    for (const [userId, counters] of this.userCounters.entries()) {
      counters.daily = 0;
      counters.lastDayReset = now;
    }

    loggers.app.info('Daily rate limit counters reset');
  }

  /**
   * Reset counters if needed (called during rate limit checks)
   */
  resetCountersIfNeeded() {
    const now = new Date();
    
    // Check if we need to reset hourly counters
    if (now.getHours() !== this.globalCounters.lastHourReset.getHours()) {
      this.resetHourlyCounters();
    }

    // Check if we need to reset daily counters
    if (now.getDate() !== this.globalCounters.lastDayReset.getDate()) {
      this.resetDailyCounters();
    }
  }

  /**
   * Get hours remaining in the current day
   * @returns {number} Hours remaining
   */
  getHoursRemainingToday() {
    const now = new Date();
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    return (endOfDay.getTime() - now.getTime()) / (1000 * 60 * 60);
  }

  /**
   * Update rate limiting configuration
   * @param {Object} newLimits - New rate limits
   */
  updateRateLimits(newLimits) {
    if (newLimits.global) {
      this.globalLimits = { ...this.globalLimits, ...newLimits.global };
    }
    
    if (newLimits.user) {
      this.userLimits = { ...this.userLimits, ...newLimits.user };
    }

    loggers.app.info('Rate limits updated', { globalLimits: this.globalLimits, userLimits: this.userLimits });
  }

  /**
   * Get current rate limiting status
   * @returns {Object} Rate limiting status
   */
  getRateLimitingStatus() {
    return {
      globalLimits: this.globalLimits,
      userLimits: this.userLimits,
      globalCounters: this.globalCounters,
      activeUsers: this.userCounters.size,
      pausedUsers: this.pausedUsers.size,
      complianceMetrics: this.complianceMetrics
    };
  }

  /**
   * Shutdown the service gracefully
   */
  shutdown() {
    // Clear any intervals if they exist
    loggers.app.info('RateLimitingService shutdown complete');
  }
}

module.exports = RateLimitingService;