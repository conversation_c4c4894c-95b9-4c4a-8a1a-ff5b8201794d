const express = require("express");
const router = express.Router();
const aiService = require("../services/aiService");
const { auth } = require("../middleware/auth");
const { logHelpers } = require("../services/logger");
const Listing = require("../models/Listing");

/**
 * @swagger
 * /api/ai/match:
 *   post:
 *     summary: AI-powered listing matching
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listing
 *               - userPreferences
 *             properties:
 *               listing:
 *                 type: object
 *                 properties:
 *                   title: { type: string }
 *                   price: { type: string }
 *                   location: { type: string }
 *                   size: { type: string }
 *                   rooms: { type: string }
 *                   propertyType: { type: string }
 *               userPreferences:
 *                 type: object
 *                 properties:
 *                   location: { type: string }
 *                   budget: { type: number }
 *                   rooms: { type: number }
 *                   propertyType: { type: string }
 *     responses:
 *       200:
 *         description: AI matching result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 score: { type: number }
 *                 matchReasoning: { type: string }
 *                 keyHighlights: { type: array }
 *                 potentialConcerns: { type: array }
 *                 recommendation: { type: string }
 */
router.post("/match", auth, async (req, res) => {
  try {
    const { userPreferences } = req.body;
    const startTime = Date.now();

    // Log incoming payload
    console.log(
      "[AI MATCH] Incoming payload:",
      JSON.stringify({ userPreferences }, null, 2)
    );

    // Fetch only the 10 most recent listings from the database
    const listings = await Listing.find({}).sort({ dateAdded: -1 }).limit(10);
    if (!listings.length) {
      return res
        .status(404)
        .json({ success: false, error: "No listings found" });
    }

    // For each listing, get the AI match score
    let bestMatch = null;
    let bestScore = -1;
    let bestResult = null;
    for (const listing of listings) {
      try {
        const result = await aiService.matchListingToUser(
          listing,
          userPreferences
        );
        if (
          result &&
          typeof result.score === "number" &&
          result.score > bestScore
        ) {
          bestScore = result.score;
          bestMatch = listing;
          bestResult = result;
        }
      } catch (err) {
        console.error("[AI MATCH] Error matching listing:", listing._id, err);
      }
    }

    if (!bestResult) {
      return res
        .status(404)
        .json({ success: false, error: "No suitable match found" });
    }

    // Log AI service result
    console.log(
      "[AI MATCH] AI service result:",
      JSON.stringify(bestResult, null, 2)
    );

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance("listing_matching", duration, "claude-3-haiku");

    res.json({
      success: true,
      data: bestResult,
      bestListing: bestMatch,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    // Log error
    console.error("[AI MATCH] Error:", error);
    logHelpers.logAiOperation("listing_matching", "error", error.message);
    res.status(500).json({
      success: false,
      error: "AI matching failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/contract/analyze:
 *   post:
 *     summary: Analyze rental contract for legal compliance
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contractText
 *             properties:
 *               contractText: { type: string }
 *               language: { type: string, default: "dutch" }
 *     responses:
 *       200:
 *         description: Contract analysis result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 riskLevel: { type: string }
 *                 complianceScore: { type: number }
 *                 keyClauses: { type: array }
 *                 potentialIssues: { type: array }
 *                 recommendations: { type: array }
 *                 summary: { type: string }
 *                 legalAdvice: { type: string }
 */
router.post("/contract/analyze", auth, async (req, res) => {
  try {
    const { contractText, language = "dutch" } = req.body;
    const startTime = Date.now();

    const result = await aiService.analyzeContract(contractText, language);

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance("contract_analysis", duration, "gpt-4o-mini");

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("contract_analysis", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Contract analysis failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/application/generate:
 *   post:
 *     summary: Generate personalized application message
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listing
 *               - userProfile
 *             properties:
 *               listing:
 *                 type: object
 *                 properties:
 *                   title: { type: string }
 *                   location: { type: string }
 *                   price: { type: string }
 *               userProfile:
 *                 type: object
 *                 properties:
 *                   name: { type: string }
 *                   income: { type: number }
 *                   occupation: { type: string }
 *               template: { type: string, default: "professional" }
 *     responses:
 *       200:
 *         description: Generated application message
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message: { type: string }
 *                 template: { type: string }
 *                 generatedAt: { type: string }
 */
router.post("/application/generate", auth, async (req, res) => {
  try {
    const { listing, userProfile, template = "professional" } = req.body;
    const startTime = Date.now();

    // Validate required fields
    if (!listing || !userProfile) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        details: "Both listing and userProfile are required",
      });
    }

    let result;
    try {
      result = await aiService.generateApplicationMessage(
        listing,
        userProfile,
        template
      );
    } catch (aiError) {
      console.log("AI service failed, using fallback:", aiError.message);
      
      // Fallback response when AI service is not available
      result = {
        message: `Dear Landlord,

I am writing to express my strong interest in your ${template} rental property: ${listing.title || 'property'}.

As a ${userProfile.occupation || 'professional'} with a stable income${userProfile.income ? ` of €${userProfile.income}` : ''}, I believe I would be an excellent tenant for your property. I am looking for a ${template === 'student' ? 'quiet place to study' : template === 'expat' ? 'comfortable home as I settle in the Netherlands' : 'long-term rental'} and am committed to maintaining the property in excellent condition.

${listing.price ? `The asking price of €${listing.price} fits well within my budget, ` : ''}and I am prepared to provide all necessary documentation including proof of income, references, and a security deposit.

I would be delighted to schedule a viewing at your earliest convenience and am available for any questions you may have.

Thank you for your consideration.

Best regards,
${userProfile.name || 'Applicant'}`,
        subject: `Application for ${listing.title || 'Rental Property'}`,
        template,
        personalizedElements: ["Property title", "User occupation", "Budget alignment"],
        tips: [
          "Follow up within 24-48 hours if no response",
          "Be prepared with all required documents",
          "Show genuine interest during viewing"
        ],
        generatedAt: new Date().toISOString(),
        fallback: true,
      };
    }

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance(
      "application_generation",
      duration,
      result.fallback ? "fallback" : "ai-service"
    );

    res.json({
      status: "success",
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    console.error("Application generation error:", error);
    logHelpers.logAiOperation("application_generation", "error", error.message);
    res.status(500).json({
      status: "error",
      error: "Application generation failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/market/analyze:
 *   post:
 *     summary: Analyze market trends and provide predictions
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - location
 *               - propertyType
 *             properties:
 *               location: { type: string }
 *               propertyType: { type: string }
 *               historicalData: { type: object }
 *     responses:
 *       200:
 *         description: Market analysis result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 marketTrend: { type: string }
 *                 pricePrediction: { type: string }
 *                 demandLevel: { type: string }
 *                 keyInsights: { type: array }
 *                 recommendations: { type: array }
 *                 confidenceScore: { type: number }
 */
router.post("/market/analyze", auth, async (req, res) => {
  try {
    const { location, propertyType, historicalData = {} } = req.body;
    const startTime = Date.now();

    const result = await aiService.analyzeMarketTrends(
      location,
      propertyType,
      historicalData
    );

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance("market_analysis", duration, "gpt-4o-mini");

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("market_analysis", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Market analysis failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/listing/summarize:
 *   post:
 *     summary: Generate smart listing summary
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listing
 *             properties:
 *               listing:
 *                 type: object
 *                 properties:
 *                   title: { type: string }
 *                   price: { type: string }
 *                   location: { type: string }
 *                   size: { type: string }
 *                   rooms: { type: string }
 *                   propertyType: { type: string }
 *               language: { type: string, default: "english" }
 *     responses:
 *       200:
 *         description: Listing summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 summary: { type: string }
 *                 language: { type: string }
 *                 generatedAt: { type: string }
 */
router.post("/listing/summarize", auth, async (req, res) => {
  try {
    const { listing, language = "english" } = req.body;
    const startTime = Date.now();

    const result = await aiService.summarizeListing(listing, language);

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance(
      "listing_summarization",
      duration,
      "llama-3.1-8b"
    );

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("listing_summarization", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Listing summarization failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/translate:
 *   post:
 *     summary: Translate real estate content
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *               - fromLanguage
 *               - toLanguage
 *             properties:
 *               content: { type: string }
 *               fromLanguage: { type: string }
 *               toLanguage: { type: string }
 *     responses:
 *       200:
 *         description: Translated content
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 original: { type: string }
 *                 translation: { type: string }
 *                 fromLanguage: { type: string }
 *                 toLanguage: { type: string }
 *                 translatedAt: { type: string }
 */
router.post("/translate", auth, async (req, res) => {
  try {
    const { content, fromLanguage, toLanguage } = req.body;
    const startTime = Date.now();

    const result = await aiService.translateContent(
      content,
      fromLanguage,
      toLanguage
    );

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance(
      "content_translation",
      duration,
      "gemini-flash-1.5"
    );

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("content_translation", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Translation failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/market-analysis:
 *   post:
 *     summary: Get market analysis data
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               city:
 *                 type: string
 *                 description: City name for market analysis
 *               propertyType:
 *                 type: string
 *                 description: Property type filter
 *               userPreferences:
 *                 type: object
 *                 description: User preferences for enhanced analysis
 *     responses:
 *       200:
 *         description: Market analysis data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 averagePrice:
 *                   type: number
 *                 priceRange:
 *                   type: object
 *                   properties:
 *                     min: { type: number }
 *                     max: { type: number }
 *                 marketTrends:
 *                   type: array
 *                   items:
 *                     type: object
 *                 insights:
 *                   type: array
 *                   items:
 *                     type: string
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 */
router.post("/market-analysis", async (req, res) => {
  try {
    console.log("Market Analysis Request Body:", JSON.stringify(req.body, null, 2));
    const { city, location, propertyType, userPreferences, priceRange: requestPriceRange, timeframe } = req.body;
    const startTime = Date.now();

    console.log('AI Market Analysis Request:', {
      location: location || city,
      propertyType,
      priceRange: requestPriceRange,
      timeframe
    });
    
    // Add detailed logging to help diagnose issues
    console.log('Starting market analysis with parameters:', {
      searchLocation: location || city,
      propertyType,
      hasUserPreferences: !!userPreferences,
      requestedTimeframe: timeframe || 'default'
    });

    // Use location field if provided, otherwise fall back to city
    const searchLocation = location || city;

    // Build query filter
    const filter = {};
    if (searchLocation) {
      filter.location = new RegExp(searchLocation, 'i');
    }
    if (propertyType) {
      filter.propertyType = new RegExp(propertyType, 'i');
    }

    // Get listings for analysis
    const listings = await Listing.find(filter).limit(1000);

    if (listings.length === 0) {
      return res.status(200).json({
        averagePrice: 0,
        priceRange: { min: 0, max: 0 },
        marketTrends: [],
        insights: ["No data available for the specified criteria"],
        totalListings: 0
      });
    }

    // Calculate market metrics
    const prices = listings
      .map(listing => {
        // Try to extract numeric price from price string
        if (!listing.price) return 0;

        try {
          // Handle different price formats
          // Extract numbers with dots or commas as thousand separators (€3.950 or €3,950)
          const priceMatch = listing.price.match(/€\s*([\d.,]+)/);
          if (priceMatch) {
            const extractedPrice = priceMatch[1].trim();
            
            // Case 1: Format with dots as thousands separators (e.g., 3.950)
            if (extractedPrice.includes('.') && !extractedPrice.includes(',')) {
              const cleanPrice = extractedPrice.replace(/\./g, '');
              return parseFloat(cleanPrice);
            }
            
            // Case 2: Format with commas as thousands separators (e.g., 3,950)
            if (extractedPrice.includes(',') && !extractedPrice.includes('.')) {
              const cleanPrice = extractedPrice.replace(/,/g, '');
              return parseFloat(cleanPrice);
            }
            
            // Case 3: Format with both dots and commas (e.g., 1.234,56)
            if (extractedPrice.includes('.') && extractedPrice.includes(',')) {
              const cleanPrice = extractedPrice.replace(/\./g, '').replace(',', '.');
              return parseFloat(cleanPrice);
            }
            
            // Case 4: Simple number
            return parseFloat(extractedPrice.replace(',', '.'));
          }
          
          // Fallback to simple number extraction
          const simpleMatch = listing.price.match(/\d+/);
          return simpleMatch ? parseInt(simpleMatch[0]) : 0;
        } catch (parseError) {
          console.error('Error parsing price:', listing.price, parseError);
          return 0;
        }
      })
      .filter(price => price > 0);

    // Add safety checks for price calculations
    let averagePrice = 0;
    let priceRange = { min: 0, max: 0 };

    try {
      if (prices.length > 0) {
        // Calculate average price with safety checks
        const sum = prices.reduce((acc, price) => acc + (isNaN(price) ? 0 : price), 0);
        averagePrice = Math.round(sum / prices.length);

        // Calculate price range with safety checks
        priceRange = {
          min: Math.min(...prices.filter(p => !isNaN(p))),
          max: Math.max(...prices.filter(p => !isNaN(p)))
        };

        // Additional validation
        if (isNaN(averagePrice)) averagePrice = 0;
        if (isNaN(priceRange.min)) priceRange.min = 0;
        if (isNaN(priceRange.max)) priceRange.max = 0;
      }
    } catch (calcError) {
      console.error('Error calculating price metrics:', calcError);
      averagePrice = 0;
      priceRange = { min: 0, max: 0 };
    }

    // Generate market insights
    const insights = [];
    if (prices.length > 0) {
      insights.push(`Average price: €${averagePrice.toLocaleString()}`);
      insights.push(`Price range: €${priceRange.min.toLocaleString()} - €${priceRange.max.toLocaleString()}`);
      insights.push(`Total listings analyzed: ${listings.length}`);

      if (city) {
        insights.push(`Market data for ${city}`);
      }
      if (propertyType) {
        insights.push(`Property type: ${propertyType}`);
      }
    }

    // Simple market trends (could be enhanced with time-series data)
    const marketTrends = [
      { period: "current", averagePrice, listingCount: listings.length }
    ];

    const response = {
      success: true,
      data: {
        location: searchLocation || "Unknown",
        averagePrice,
        priceRange: {
          min: priceRange.min,
          max: priceRange.max
        },
        marketTrend: "stable", // Default value
        pricePrediction: "Prices are expected to remain stable",
        demandLevel: "medium",
        keyInsights: insights,
        recommendations: [
          "Set a budget based on your financial situation",
          "Consider multiple neighborhoods to increase options"
        ],
        confidenceScore: 70,
        dataPoints: listings.length
      },
      marketTrends,
      insights,
      totalListings: listings.length
    };

    console.log(`Market analysis completed in ${Date.now() - startTime}ms`);
    console.log('Market analysis response structure:', Object.keys(response));
    res.json(response);

  } catch (error) {
    console.error('AI Market Analysis Error:', error);
    console.error('Error stack:', error.stack);
    
    // Log more details about the error context
    console.error('Error context:', {
      errorName: error.name,
      errorCode: error.code,
      errorType: typeof error
    });
    
    // Provide more detailed error information
    res.status(500).json({
      success: false,
      error: "Internal server error",
      message: "Failed to generate market analysis",
      details: error.message || "Unknown error occurred",
      code: error.code || "MARKET_ANALYSIS_ERROR"
    });
  }
});

// AI-powered property matching endpoint
router.post("/match", auth, async (req, res) => {
  try {
    const { userProfile, preferences, maxResults = 50 } = req.body;
    const startTime = Date.now();

    logHelpers.logInfo(req, "AI Property Matching Request", {
      userId: req.user?.id,
      preferences: {
        locations: preferences?.preferredLocations?.length || 0,
        propertyTypes: preferences?.propertyTypes?.length || 0,
        priceRange: `${preferences?.minPrice || 0}-${preferences?.maxPrice || 'unlimited'}`,
        rooms: `${preferences?.minRooms || 0}-${preferences?.maxRooms || 'unlimited'}`
      },
      maxResults
    });

    // Validate required fields
    if (!preferences) {
      return res.status(400).json({
        status: "error",
        error: "User preferences are required",
        message: "Please provide user preferences for matching"
      });
    }

    // Get available listings from database
    const query = {};

    // Apply basic filters based on preferences
    if (preferences.preferredLocations && preferences.preferredLocations.length > 0) {
      query.location = { $in: preferences.preferredLocations.map(loc => new RegExp(loc, 'i')) };
    }

    if (preferences.propertyTypes && preferences.propertyTypes.length > 0) {
      query.propertyType = { $in: preferences.propertyTypes };
    }

    if (preferences.minPrice || preferences.maxPrice) {
      query.price = {};
      if (preferences.minPrice) query.price.$gte = preferences.minPrice;
      if (preferences.maxPrice) query.price.$lte = preferences.maxPrice;
    }

    if (preferences.minRooms || preferences.maxRooms) {
      query.rooms = {};
      if (preferences.minRooms) query.rooms.$gte = preferences.minRooms;
      if (preferences.maxRooms) query.rooms.$lte = preferences.maxRooms;
    }

    // Fetch listings from database
    const listings = await Listing.find(query)
      .limit(maxResults * 2) // Get more than needed for better matching
      .sort({ createdAt: -1 })
      .lean();

    logHelpers.logInfo(req, "Listings fetched for matching", {
      totalFound: listings.length,
      query: JSON.stringify(query)
    });

    // Use AI service to score and rank matches
    const matchingRequest = {
      userProfile,
      preferences,
      listings,
      maxResults
    };

    const aiResult = await aiService.getPropertyMatches(matchingRequest);

    // Format response
    const response = {
      status: "success",
      data: {
        matches: aiResult.matches || [],
        totalAnalyzed: aiResult.totalAnalyzed || listings.length,
        averageScore: aiResult.averageScore || 0,
        recommendations: aiResult.recommendations || []
      },
      message: `Found ${aiResult.matches?.length || 0} property matches`,
      processingTime: Date.now() - startTime
    };

    logHelpers.logInfo(req, "AI Property Matching Completed", {
      matchesFound: aiResult.matches?.length || 0,
      averageScore: aiResult.averageScore || 0,
      processingTime: response.processingTime
    });

    res.json(response);

  } catch (error) {
    console.error('AI Property Matching Error:', error);
    res.status(500).json({
      status: "error",
      error: "Internal server error",
      message: "Failed to generate property matches"
    });
  }
});

module.exports = router;
