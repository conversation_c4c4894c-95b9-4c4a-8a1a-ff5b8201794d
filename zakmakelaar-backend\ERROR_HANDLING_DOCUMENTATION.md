# Comprehensive Error Handling and Recovery System

## Overview

The ZakMakelaar auto-application system implements a comprehensive error handling and recovery system designed to handle various types of errors that can occur during automated property applications. The system provides intelligent error categorization, automatic retry mechanisms, graceful degradation, and manual intervention triggers.

## Architecture

### Core Components

1. **ErrorHandlingService** - Central error handling and recovery service
2. **ErrorHandlingMiddleware** - Express middleware for API error handling
3. **Integration Points** - Error handling integration in all major services

### Error Categories

The system categorizes errors into the following types:

#### 1. Network Errors (`NETWORK`)
- **Description**: Connection timeouts, DNS failures, network interruptions
- **Retryable**: Yes (up to 3 attempts)
- **Backoff**: Exponential (5s base, 2x multiplier, 5min max)
- **Examples**: `ECONNRESET`, `ETIMEDOUT`, `ENOTFOUND`

#### 2. Form Errors (`FORM`)
- **Description**: Form detection failures, field mapping issues, validation errors
- **Retryable**: Yes (up to 2 attempts with adaptation)
- **Backoff**: Exponential (10s base, 1.5x multiplier, 1min max)
- **Examples**: `selector not found`, `validation failed`, `form submission failed`

#### 3. Detection Errors (`DETECTION`)
- **Description**: Bot detection, CAPTCHA challenges, access restrictions
- **Retryable**: No
- **Action**: Pause user, require manual intervention
- **Examples**: `bot detected`, `access denied`, `suspicious activity`

#### 4. Data Errors (`DATA`)
- **Description**: Missing user information, invalid data, schema validation failures
- **Retryable**: No
- **Action**: Notify user, pause until data is fixed
- **Examples**: `user not found`, `missing required field`, `invalid data`

#### 5. System Errors (`SYSTEM`)
- **Description**: Database errors, service unavailability, internal failures
- **Retryable**: Yes (up to 2 attempts)
- **Backoff**: Exponential (30s base, 3x multiplier, 10min max)
- **Examples**: `database error`, `service unavailable`, `internal server error`

#### 6. Rate Limit Errors (`RATE_LIMIT`)
- **Description**: API rate limits, quota exceeded, throttling
- **Retryable**: Yes (up to 5 attempts)
- **Backoff**: Exponential (1min base, 2x multiplier, 1hr max)
- **Examples**: `rate limit exceeded`, `too many requests`, `quota exceeded`

#### 7. CAPTCHA Errors (`CAPTCHA`)
- **Description**: CAPTCHA verification required
- **Retryable**: No
- **Action**: Notify user, require manual verification
- **Examples**: `CAPTCHA required`, `human verification needed`

#### 8. Authentication Errors (`AUTHENTICATION`)
- **Description**: Login failures, session expiry, invalid credentials
- **Retryable**: No (except for session refresh)
- **Action**: Notify user, require re-authentication
- **Examples**: `authentication failed`, `session expired`, `invalid credentials`

## Recovery Strategies

### Automatic Retry with Exponential Backoff

```javascript
// Example backoff calculation
const delay = baseDelay * Math.pow(multiplier, attemptNumber - 1);
const jitter = delay * 0.1 * (Math.random() - 0.5); // ±10% jitter
const finalDelay = Math.min(delay + jitter, maxDelay);
```

### Intelligent Adaptation

For form errors, the system adapts its approach:
- Use alternative selectors
- Increase timeouts
- Enable additional validation
- Update form configuration

### Graceful Degradation

When system-wide failures are detected:
- Pause all queue processing
- Enable degraded mode
- Send system-wide notifications
- Maintain core functionality

### Manual Intervention Triggers

Certain errors require manual intervention:
- CAPTCHA challenges
- Bot detection
- Missing user data
- Authentication failures

## Usage Examples

### Basic Error Handling

```javascript
const ErrorHandlingService = require('./services/errorHandlingService');

const errorHandler = new ErrorHandlingService();

try {
  // Some operation that might fail
  await riskyOperation();
} catch (error) {
  const recoveryResult = await errorHandler.handleError(error, {
    service: 'MyService',
    userId: 'user123',
    operation: 'risky_operation'
  });
  
  if (recoveryResult.retryScheduled) {
    console.log(`Retry scheduled in ${recoveryResult.retryDelay}ms`);
  } else if (recoveryResult.manualInterventionRequired) {
    console.log('Manual intervention required');
  }
}
```

### Service Integration

```javascript
class MyService {
  constructor() {
    this.errorHandlingService = new ErrorHandlingService();
  }
  
  async processItem(item) {
    try {
      // Process the item
      return await this.doProcessing(item);
    } catch (error) {
      const recoveryResult = await this.errorHandlingService.handleError(error, {
        service: 'MyService',
        method: 'processItem',
        itemId: item.id,
        userId: item.userId
      });
      
      // Handle based on recovery result
      if (!recoveryResult.retryScheduled) {
        throw error; // Re-throw if not handled
      }
    }
  }
}
```

### Express Middleware Usage

```javascript
const { errorHandlingMiddleware } = require('./middleware/errorHandlingMiddleware');

// Use as error handling middleware
app.use(errorHandlingMiddleware.handleError);

// Use async handler wrapper
app.get('/api/data', errorHandlingMiddleware.asyncHandler(async (req, res) => {
  const data = await getData();
  res.json(data);
}));
```

## Configuration

### Error Categories Configuration

Each error category can be configured with:

```javascript
const errorCategory = {
  name: 'Network Error',
  retryable: true,
  maxRetries: 3,
  backoffMultiplier: 2,
  baseDelay: 5000, // 5 seconds
  maxDelay: 300000, // 5 minutes
  requiresManualIntervention: false
};
```

### Pattern Matching

Error patterns are used for automatic categorization:

```javascript
const errorPatterns = {
  NETWORK: [
    /network|connection|timeout|ECONNRESET|ENOTFOUND|ETIMEDOUT/i,
    /fetch.*failed|request.*failed|socket.*hang/i
  ],
  FORM: [
    /form.*not.*found|field.*not.*found|selector.*not.*found/i,
    /validation.*failed|required.*field|invalid.*input/i
  ]
  // ... more patterns
};
```

## Monitoring and Metrics

### Health Metrics

The system tracks various health metrics:

```javascript
const healthMetrics = {
  totalErrors: 0,
  errorsByCategory: {},
  errorsByUser: {},
  systemFailures: 0,
  lastSystemFailure: null,
  recoverySuccessRate: 0,
  averageRecoveryTime: 0
};
```

### Health Check Endpoint

```http
GET /api/error-handling/health
```

Response:
```json
{
  "success": true,
  "errorHandling": {
    "status": "operational",
    "statistics": {
      "totalErrors": 42,
      "errorsByCategory": {
        "NETWORK": 15,
        "FORM": 12,
        "SYSTEM": 8
      }
    }
  }
}
```

## API Integration

### Error Response Format

All API errors follow a consistent format:

```json
{
  "success": false,
  "error": "Error Type",
  "message": "Human-readable error message",
  "requestId": "req-123",
  "retryInfo": {
    "retryScheduled": true,
    "retryDelay": 30000,
    "retryAttempt": 2
  },
  "manualInterventionRequired": false
}
```

### Status Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource not found)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error (system errors)
- `503` - Service Unavailable (temporary failures)

## Testing

### Unit Tests

```javascript
describe('ErrorHandlingService', () => {
  test('should categorize network errors correctly', () => {
    const error = new Error('ECONNRESET');
    const result = errorHandler.categorizeError(error, {});
    expect(result.category).toBe('NETWORK');
  });
  
  test('should calculate exponential backoff', () => {
    const delay = errorHandler.calculateBackoffDelay('NETWORK', 2, config);
    expect(delay).toBeGreaterThan(config.baseDelay);
  });
});
```

### Integration Tests

```javascript
describe('Error Handling Integration', () => {
  test('should handle queue item errors with retry', async () => {
    const error = new Error('Processing failed');
    const result = await errorHandler.handleError(error, {
      queueItemId: 'queue123',
      userId: 'user123'
    });
    
    expect(result.retryScheduled).toBe(true);
  });
});
```

## Best Practices

### 1. Provide Context

Always provide rich context when handling errors:

```javascript
const context = {
  service: 'ServiceName',
  method: 'methodName',
  userId: 'user123',
  operation: 'specific_operation',
  attemptNumber: 1,
  additionalData: {}
};
```

### 2. Use Appropriate Error Types

Create specific error types for different scenarios:

```javascript
class ValidationError extends Error {
  constructor(message, field) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}
```

### 3. Log Comprehensively

Include all relevant information in error logs:

```javascript
logger.error('Operation failed', {
  error: error.message,
  stack: error.stack,
  context,
  recoveryResult,
  timestamp: new Date().toISOString()
});
```

### 4. Handle Gracefully

Always handle errors gracefully without breaking the user experience:

```javascript
try {
  await riskyOperation();
} catch (error) {
  const recovery = await handleError(error, context);
  
  if (recovery.manualInterventionRequired) {
    // Notify user appropriately
    notifyUser('Action required', recovery.userMessage);
  }
  
  // Continue with fallback or alternative flow
}
```

## Troubleshooting

### Common Issues

1. **High Error Rates**
   - Check system health metrics
   - Review error patterns
   - Verify external service availability

2. **Stuck Queue Items**
   - Monitor processing times
   - Check for deadlocks
   - Review retry configurations

3. **User Complaints**
   - Check user-specific error rates
   - Review notification delivery
   - Verify manual intervention triggers

### Debugging

Enable debug logging:

```javascript
process.env.LOG_LEVEL = 'debug';
```

Check error statistics:

```javascript
const stats = errorHandler.getErrorStatistics();
console.log('Error statistics:', stats);
```

Reset statistics for fresh monitoring:

```javascript
errorHandler.resetErrorStatistics();
```

## Future Enhancements

1. **Machine Learning Integration**
   - Predictive error detection
   - Adaptive retry strategies
   - Pattern recognition improvements

2. **Advanced Monitoring**
   - Real-time dashboards
   - Alerting integration
   - Performance analytics

3. **User Experience**
   - Better error messages
   - Self-service recovery options
   - Proactive notifications

4. **System Resilience**
   - Circuit breaker patterns
   - Bulkhead isolation
   - Chaos engineering

## Conclusion

The comprehensive error handling and recovery system provides robust error management for the ZakMakelaar auto-application platform. It ensures system reliability, improves user experience, and provides administrators with the tools needed to monitor and maintain system health.

For questions or issues, please refer to the test files or contact the development team.