// Debug user data to check property owner status
async function debugUserData() {
  console.log('🔍 Debugging user data for property owner detection...\n');
  
  try {
    // Step 1: Login and get user data
    console.log('1️⃣ Logging in as property owner...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    if (!loginResponse.ok) {
      console.log('❌ Login failed:', loginResponse.status);
      return;
    }
    
    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    
    // Step 2: Analyze user data structure
    console.log('\n2️⃣ Analyzing user data structure...');
    const user = loginData.data?.user || loginData.user;
    
    console.log('📋 User data analysis:');
    console.log('- User ID:', user._id);
    console.log('- Email:', user.email);
    console.log('- Role:', user.role);
    console.log('- Has propertyOwner object:', !!user.propertyOwner);
    
    if (user.propertyOwner) {
      console.log('- propertyOwner.isPropertyOwner:', user.propertyOwner.isPropertyOwner);
      console.log('- propertyOwner.verificationStatus:', user.propertyOwner.verificationStatus);
      console.log('- propertyOwner.properties length:', user.propertyOwner.properties?.length || 0);
    }
    
    // Step 3: Test navigation conditions
    console.log('\n3️⃣ Testing navigation conditions...');
    
    const condition1 = user.role === 'owner';
    const condition2 = user.propertyOwner && user.propertyOwner.isPropertyOwner;
    const shouldGoToPropertyOwnerDashboard = condition1 || condition2;
    
    console.log('Navigation conditions:');
    console.log('- user.role === "owner":', condition1);
    console.log('- user.propertyOwner?.isPropertyOwner:', condition2);
    console.log('- Should go to property owner dashboard:', shouldGoToPropertyOwnerDashboard);
    
    // Step 4: Check what the frontend auth service would return
    console.log('\n4️⃣ Testing frontend auth service transformation...');
    
    // Simulate the frontend API service transformation
    const frontendTransformedData = {
      success: true,
      data: {
        user: user,
        token: loginData.token
      }
    };
    
    console.log('Frontend would receive:');
    console.log('- success:', frontendTransformedData.success);
    console.log('- user.role:', frontendTransformedData.data.user.role);
    console.log('- user.propertyOwner?.isPropertyOwner:', frontendTransformedData.data.user.propertyOwner?.isPropertyOwner);
    
    // Step 5: Test auth store logic
    console.log('\n5️⃣ Testing auth store logic...');
    
    const authStoreWouldSetUser = frontendTransformedData.success && frontendTransformedData.data;
    console.log('Auth store would set user:', authStoreWouldSetUser);
    
    if (authStoreWouldSetUser) {
      const storedUser = frontendTransformedData.data.user;
      const navigationCondition1 = storedUser.role === 'owner';
      const navigationCondition2 = storedUser.propertyOwner && storedUser.propertyOwner.isPropertyOwner;
      
      console.log('Stored user navigation conditions:');
      console.log('- role === "owner":', navigationCondition1);
      console.log('- propertyOwner?.isPropertyOwner:', navigationCondition2);
      console.log('- Final decision:', navigationCondition1 || navigationCondition2 ? 'Property Owner Dashboard' : 'Regular Dashboard');
    }
    
    console.log('\n🎯 Summary:');
    if (shouldGoToPropertyOwnerDashboard) {
      console.log('✅ User should be redirected to property owner dashboard');
      console.log('💡 If you\'re being redirected to regular dashboard, the issue is in:');
      console.log('   1. Frontend auth service not preserving user data correctly');
      console.log('   2. Navigation service not reading cached user data correctly');
      console.log('   3. Auth store not setting user data correctly');
    } else {
      console.log('❌ User data indicates regular user, not property owner');
      console.log('💡 Check if the mock data script set the user role correctly');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugUserData();