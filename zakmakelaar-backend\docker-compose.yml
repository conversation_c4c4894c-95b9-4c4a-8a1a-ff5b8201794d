version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: zakmakelaar-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: zakmakelaar
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - zakmakelaar-network

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: zakmakelaar-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - zakmakelaar-network

  # Backend Application
  backend:
    build: .
    container_name: zakmakelaar-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - MONGO_URI=**********************************************************************
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - JWT_SECRET=${JWT_SECRET:-your-super-secure-jwt-secret-key-here-make-it-long-and-random}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - SENDGRID_FROM_EMAIL=${SENDGRID_FROM_EMAIL}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - TWILIO_WHATSAPP_FROM=${TWILIO_WHATSAPP_FROM}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - OPENROUTER_DEFAULT_MODEL=${OPENROUTER_DEFAULT_MODEL:-deepseek/deepseek-r1-0528:free}
      - OPENROUTER_MAX_TOKENS=${OPENROUTER_MAX_TOKENS:-4000}
      - OPENROUTER_TEMPERATURE=${OPENROUTER_TEMPERATURE:-0.7}
      - OPENROUTER_ANALYSIS_MODEL=${OPENROUTER_ANALYSIS_MODEL:-deepseek/deepseek-r1-0528:free}
      - OPENROUTER_MATCHING_MODEL=${OPENROUTER_MATCHING_MODEL:-deepseek/deepseek-r1-0528:free}
      - OPENROUTER_SUMMARIZATION_MODEL=${OPENROUTER_SUMMARIZATION_MODEL:-deepseek/deepseek-r1-0528:free}
      - OPENROUTER_TRANSLATION_MODEL=${OPENROUTER_TRANSLATION_MODEL:-deepseek/deepseek-r1-0528:free}
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-900000}
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-100}
      - SCRAPING_INTERVAL_MINUTES=${SCRAPING_INTERVAL_MINUTES:-5}
      - SCRAPING_TIMEOUT_MS=${SCRAPING_TIMEOUT_MS:-60000}
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:3001}
      - CACHE_DEFAULT_TTL=${CACHE_DEFAULT_TTL:-3600}
      - CACHE_LISTINGS_TTL=${CACHE_LISTINGS_TTL:-300}
      - CACHE_USER_TTL=${CACHE_USER_TTL:-1800}
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - zakmakelaar-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  zakmakelaar-network:
    driver: bridge
