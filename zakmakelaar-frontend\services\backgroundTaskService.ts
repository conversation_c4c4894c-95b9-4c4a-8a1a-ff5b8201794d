import * as BackgroundFetch from 'expo-background-fetch';
import * as TaskManager from 'expo-task-manager';
import { Platform } from 'react-native';
import { useAuthStore } from '../store/authStore';
import { useAIStore } from '../store/aiStore';
import { useNotificationStore } from '../store/notificationStore';
import { aiService } from './aiService';

// Define task names
export const BACKGROUND_MATCHING_TASK = 'background-property-matching';
export const BACKGROUND_NOTIFICATION_CHECK = 'background-notification-check';

// Register background tasks
export const registerBackgroundTasks = async () => {
  try {
    // Register property matching task
    TaskManager.defineTask(BACKGROUND_MATCHING_TASK, async () => {
      try {
        const user = useAuthStore.getState().user;
        
        // Skip if no user or no preferences
        if (!user || !user.preferences) {
          return BackgroundFetch.BackgroundFetchResult.NoData;
        }
        
        // Get last match update time
        const lastMatchUpdate = useAIStore.getState().lastMatchUpdate;
        const now = new Date();
        
        // Only run if it's been at least 1 hour since last update
        if (lastMatchUpdate && (now.getTime() - lastMatchUpdate.getTime() < 3600000)) {
          return BackgroundFetch.BackgroundFetchResult.NoData;
        }
        
        // Run property matching
        const response = await aiService.getPropertyMatches({
          userProfile: user,
          preferences: user.preferences,
          maxResults: 20,
        });
        
        if (response.success && response.data) {
          // Process new matches
          const newMatches = response.data.matches.map((match, index) => ({
            ...match,
            id: `match_${Date.now()}_${index}`,
            timestamp: new Date(),
            viewed: false,
            applied: false,
            saved: false,
          }));
          
          // Get existing matches
          const existingMatches = useAIStore.getState().matches;
          
          // Find truly new matches (not in existing matches)
          const trulyNewMatches = newMatches.filter(newMatch => 
            !existingMatches.some(existingMatch => 
              existingMatch.listing._id === newMatch.listing._id
            )
          );
          
          // Update matches in store
          if (trulyNewMatches.length > 0) {
            // Update store with new matches
            useAIStore.getState().matches = [
              ...trulyNewMatches,
              ...existingMatches
            ];
            
            // Update last match update time
            useAIStore.getState().lastMatchUpdate = new Date();
            
            // Send notification for new matches
            if (trulyNewMatches.length > 0) {
              const notificationStore = useNotificationStore.getState();
              
              await notificationStore.scheduleLocalNotification({
                title: `${trulyNewMatches.length} New Property Matches`,
                body: `We found ${trulyNewMatches.length} new properties that match your preferences!`,
                data: { type: 'new_matches', count: trulyNewMatches.length },
              });
            }
            
            return BackgroundFetch.BackgroundFetchResult.NewData;
          }
        }
        
        return BackgroundFetch.BackgroundFetchResult.NoData;
      } catch (error) {
        console.error('Background matching task error:', error);
        return BackgroundFetch.BackgroundFetchResult.Failed;
      }
    });
    
    // Register notification check task
    TaskManager.defineTask(BACKGROUND_NOTIFICATION_CHECK, async () => {
      try {
        const user = useAuthStore.getState().user;
        
        // Skip if no user
        if (!user) {
          return BackgroundFetch.BackgroundFetchResult.NoData;
        }
        
        // Check for new notifications (implementation depends on your backend)
        // This is a placeholder for the actual implementation
        
        return BackgroundFetch.BackgroundFetchResult.NoData;
      } catch (error) {
        console.error('Background notification check error:', error);
        return BackgroundFetch.BackgroundFetchResult.Failed;
      }
    });
    
    // Register background fetch for property matching
    await BackgroundFetch.registerTaskAsync(BACKGROUND_MATCHING_TASK, {
      minimumInterval: 60 * 60, // 1 hour
      stopOnTerminate: false,
      startOnBoot: true,
    });
    
    // Register background fetch for notification check
    await BackgroundFetch.registerTaskAsync(BACKGROUND_NOTIFICATION_CHECK, {
      minimumInterval: 15 * 60, // 15 minutes
      stopOnTerminate: false,
      startOnBoot: true,
    });
    
    console.log('Background tasks registered successfully');
    return true;
  } catch (error) {
    console.error('Failed to register background tasks:', error);
    return false;
  }
};

// Unregister background tasks
export const unregisterBackgroundTasks = async () => {
  try {
    await BackgroundFetch.unregisterTaskAsync(BACKGROUND_MATCHING_TASK);
    await BackgroundFetch.unregisterTaskAsync(BACKGROUND_NOTIFICATION_CHECK);
    console.log('Background tasks unregistered successfully');
    return true;
  } catch (error) {
    console.error('Failed to unregister background tasks:', error);
    return false;
  }
};

// Check if background fetch is available
export const isBackgroundFetchAvailable = async () => {
  const status = await BackgroundFetch.getStatusAsync();
  return status === BackgroundFetch.BackgroundFetchStatus.Available;
};

// Get background fetch status
export const getBackgroundFetchStatus = async () => {
  return await BackgroundFetch.getStatusAsync();
};

// Manually trigger a background fetch (for testing)
export const triggerBackgroundFetch = async (taskName: string) => {
  try {
    const result = await TaskManager.isTaskRegisteredAsync(taskName);
    if (!result) {
      console.warn(`Task ${taskName} is not registered`);
      return false;
    }
    
    await BackgroundFetch.setMinimumIntervalAsync(1); // Set to minimum for testing
    const fetchResult = await BackgroundFetch.fetchAsync(taskName);
    
    // Reset to normal interval
    if (taskName === BACKGROUND_MATCHING_TASK) {
      await BackgroundFetch.setMinimumIntervalAsync(60 * 60); // 1 hour
    } else if (taskName === BACKGROUND_NOTIFICATION_CHECK) {
      await BackgroundFetch.setMinimumIntervalAsync(15 * 60); // 15 minutes
    }
    
    return fetchResult === BackgroundFetch.BackgroundFetchResult.NewData;
  } catch (error) {
    console.error(`Failed to trigger background fetch for ${taskName}:`, error);
    return false;
  }
};

// Check if a task is registered
export const isTaskRegistered = async (taskName: string) => {
  return await TaskManager.isTaskRegisteredAsync(taskName);
};

// Get all registered tasks
export const getRegisteredTasks = async () => {
  return await TaskManager.getRegisteredTasksAsync();
};

// Helper function to determine if background tasks are supported on this device
export const areBackgroundTasksSupported = () => {
  // iOS 13+ and Android support background fetch
  if (Platform.OS === 'ios') {
    const majorVersion = parseInt(Platform.Version as string, 10);
    return majorVersion >= 13;
  }
  
  return Platform.OS === 'android';
};