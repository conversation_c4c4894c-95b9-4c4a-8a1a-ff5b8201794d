const express = require('express');
const router = express.Router();
const propertyOwnerController = require('../controllers/propertyOwnerController');
const { auth } = require('../middleware/auth');
const rateLimiter = require('../middleware/rateLimiter');
const validation = require('../middleware/validation');

/**
 * Property Owner Management Routes
 * 
 * All routes require authentication and are rate-limited
 * Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6
 */

// Apply authentication middleware to all routes
router.use(auth);

// Apply rate limiting - property owners get higher limits
router.use(rateLimiter.propertyOwner);

/**
 * Property Owner Registration and Verification Routes
 * Requirements: 10.1, 10.2
 */

// Register as property owner
router.post('/register', 
  validation.validatePropertyOwnerRegistration,
  propertyOwnerController.registerPropertyOwner
);

// Verify property owner business registration
router.post('/verify', 
  validation.validatePropertyOwnerVerification,
  propertyOwnerController.verifyPropertyOwner
);

// Get verification status
router.get('/verification-status', 
  propertyOwnerController.getVerificationStatus
);

/**
 * @swagger
 * components:
 *   schemas:
 *     PropertyOwnerProfile:
 *       type: object
 *       properties:
 *         businessRegistration:
 *           type: string
 *           description: Dutch KvK business registration number (8 digits)
 *           example: "12345678"
 *         companyName:
 *           type: string
 *           description: Company or business name
 *           example: "Amsterdam Properties BV"
 *         address:
 *           type: string
 *           description: Business address
 *           example: "Damrak 123, 1012 LP Amsterdam"
 *         phone:
 *           type: string
 *           description: Business phone number
 *           example: "+***********"
 *         website:
 *           type: string
 *           description: Company website URL
 *           example: "https://amsterdamproperties.nl"
 *         description:
 *           type: string
 *           description: Company description
 *           example: "Professional property management company in Amsterdam"
 *         verificationStatus:
 *           type: string
 *           enum: [pending, verified, rejected]
 *           description: Business verification status
 *           example: "pending"
 *         notificationSettings:
 *           type: object
 *           properties:
 *             email:
 *               type: boolean
 *               description: Email notifications enabled
 *               example: true
 *             push:
 *               type: boolean
 *               description: Push notifications enabled
 *               example: true
 *             sms:
 *               type: boolean
 *               description: SMS notifications enabled
 *               example: false
 */

/**
 * @swagger
 * /api/property-owner/profile:
 *   get:
 *     summary: Get property owner profile
 *     description: Retrieve the profile information for the authenticated property owner
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Property owner profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   $ref: '#/components/schemas/PropertyOwnerProfile'
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User is not a property owner
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /api/property-owner/profile:
 *   put:
 *     summary: Update property owner profile
 *     description: Update the profile information for the authenticated property owner
 *     tags: [Property Owner]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               companyName:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 description: Company or business name
 *                 example: "Amsterdam Properties BV"
 *               businessRegistration:
 *                 type: string
 *                 pattern: '^\\d{8}$'
 *                 description: Dutch KvK business registration number (8 digits)
 *                 example: "12345678"
 *               address:
 *                 type: string
 *                 minLength: 5
 *                 maxLength: 200
 *                 description: Business address
 *                 example: "Damrak 123, 1012 LP Amsterdam"
 *               phone:
 *                 type: string
 *                 pattern: '^(\\+31|0)[1-9]\\d{8}$'
 *                 description: Dutch phone number
 *                 example: "+***********"
 *               website:
 *                 type: string
 *                 format: uri
 *                 description: Company website URL
 *                 example: "https://amsterdamproperties.nl"
 *               description:
 *                 type: string
 *                 maxLength: 1000
 *                 description: Company description
 *                 example: "Professional property management company in Amsterdam"
 *               notificationSettings:
 *                 type: object
 *                 properties:
 *                   email:
 *                     type: boolean
 *                     description: Email notifications enabled
 *                     example: true
 *                   push:
 *                     type: boolean
 *                     description: Push notifications enabled
 *                     example: true
 *                   sms:
 *                     type: boolean
 *                     description: SMS notifications enabled
 *                     example: false
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Profile updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/PropertyOwnerProfile'
 *       400:
 *         description: Bad request - Validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Validation failed"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                         example: "businessRegistration"
 *                       message:
 *                         type: string
 *                         example: "Business registration must be 8 digits (KvK number)"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - User is not a property owner
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * Property Owner Profile Routes
 * Requirements: 10.1, 10.2
 */

// Get property owner profile
router.get('/profile', 
  propertyOwnerController.getOwnerProfile
);

// Update property owner profile
router.put('/profile', 
  validation.validatePropertyOwnerProfile,
  propertyOwnerController.updateOwnerProfile
);

/**
 * Property Owner Dashboard Routes
 * Requirements: 10.4, 10.5
 */

// Get property owner dashboard
router.get('/dashboard', 
  propertyOwnerController.getDashboard
);

// Get property owner statistics
router.get('/statistics', 
  propertyOwnerController.getStatistics
);

/**
 * Property Management Routes
 * Requirements: 10.3, 10.4
 */

// List all properties for the owner
router.get('/properties', 
  propertyOwnerController.getProperties
);

// Get single property details
router.get('/properties/:propertyId', 
  validation.validatePropertyId,
  propertyOwnerController.getPropertyDetails
);

// Add new property
router.post('/properties', 
  validation.validatePropertyData,
  propertyOwnerController.manageProperties
);

// Update property
router.put('/properties/:propertyId', 
  validation.validatePropertyId,
  validation.validatePropertyData,
  propertyOwnerController.manageProperties
);

// Remove property
router.delete('/properties/:propertyId', 
  validation.validatePropertyId,
  propertyOwnerController.manageProperties
);

// Activate property listing
router.put('/properties/:propertyId/activate', 
  validation.validatePropertyId,
  propertyOwnerController.activateProperty
);

// Deactivate property listing
router.put('/properties/:propertyId/deactivate', 
  validation.validatePropertyId,
  propertyOwnerController.deactivateProperty
);

/**
 * Tenant Screening and Application Management Routes
 * Requirements: 10.4, 10.5, 10.6
 */

// Screen tenants for a property
router.post('/screen-tenants/:propertyId', 
  validation.validatePropertyId,
  validation.validateScreeningRequest,
  propertyOwnerController.screenTenants
);

// Rank applicants for a property
router.post('/rank-applicants/:propertyId', 
  validation.validatePropertyId,
  validation.validateRankingCriteria,
  propertyOwnerController.rankApplicants
);

// Get applications for a property
router.get('/properties/:propertyId/applications', 
  validation.validatePropertyId,
  propertyOwnerController.getPropertyApplications
);

// Update application status
router.put('/applications/:applicationId/status', 
  validation.validateApplicationId,
  validation.validateApplicationStatusUpdate,
  propertyOwnerController.updateApplicationStatus
);

/**
 * Reporting Routes
 * Requirements: 10.6
 */

// Generate property report
router.get('/properties/:propertyId/report', 
  validation.validatePropertyId,
  validation.validateReportType,
  propertyOwnerController.generatePropertyReport
);

module.exports = router;