import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack, useRouter, useSegments } from "expo-router";
import { StatusBar } from "expo-status-bar";
import "react-native-reanimated";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { useEffect } from "react";

import { useColorScheme } from "@/hooks/useColorScheme";
import AppInitializer from "@/components/AppInitializer";
import ErrorBoundary from "@/components/ErrorBoundary";
import { useAuthStore } from "@/store/authStore";

import { QueryProvider } from "@/components/QueryProvider";

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  const segments = useSegments();
  const router = useRouter();
  const { isAuthenticated, user } = useAuthStore();

  // Handle automatic navigation based on auth state
  useEffect(() => {
    if (!segments.length) return;

    const handleNavigation = async () => {
      const currentRoute = segments[0];
      const protectedRoutes = ['dashboard', 'profile', 'application', 'contract-review', 'listing-details', 'property-owner'];
      const authRoutes = ['login'];

      // Helper function to check if preferences are valid and complete
      const hasValidPreferences = (preferences: any) => {
        if (!preferences) return false;

        // Check if preferences object exists and has required fields
        const requiredFields = [
          'minPrice', 'maxPrice', 'preferredLocations',
          'propertyTypes', 'minRooms', 'maxRooms',
          'amenities', 'notifications'
        ];

        // Check if all required fields exist and have valid values
        const hasAllFields = requiredFields.every(field => {
          const value = preferences[field];
          if (value === undefined || value === null) return false;

          // Check arrays have at least one item
          if (Array.isArray(value) && field !== 'amenities') {
            return value.length > 0;
          }

          // Check notifications object has required fields
          if (field === 'notifications') {
            return typeof value === 'object' &&
              'email' in value &&
              'push' in value &&
              'sms' in value;
          }

          return true;
        });

        return hasAllFields;
      };

      // If the user is authenticated but doesn't have valid preferences, redirect to preferences
      // BUT skip this check for property owners who don't need tenant preferences
      if (isAuthenticated && user) {
        // Check if user is a property owner
        const isPropertyOwner = user.role === 'owner' || (user.propertyOwner && user.propertyOwner.isPropertyOwner);

        console.log('Navigation Guard - User authenticated:', {
          role: user.role,
          isPropertyOwner,
          currentRoute,
          hasPropertyOwnerObject: !!user.propertyOwner,
          propertyOwnerFlag: user.propertyOwner?.isPropertyOwner
        });

        // If user is a property owner, ensure they're on the property owner routes
        if (isPropertyOwner) {
          if (currentRoute !== 'property-owner' && !currentRoute.startsWith('property-owner')) {
            console.log('Navigation Guard - Property owner not on property owner route, redirecting to property owner dashboard');
            router.replace('/property-owner/dashboard');
            return;
          }
        } else {
          // Only check preferences for tenants, not property owners
          const hasPreferences = hasValidPreferences(user.preferences);

          if (!hasPreferences &&
            currentRoute !== 'preferences' &&
            !authRoutes.includes(currentRoute)) {
            console.log('Navigation Guard - User authenticated but no valid preferences, redirecting to preferences');
            router.replace('/preferences');
          }
        }
      } else if (!isAuthenticated) {
        // If the user is not authenticated and trying to access protected routes
        if (protectedRoutes.includes(currentRoute)) {
          console.log('User not authenticated, checking welcome screen status');
          
          try {
            const { welcomeService } = await import('../services/welcomeService');
            const hasSeenWelcome = await welcomeService.hasSeenWelcome();
            
            if (hasSeenWelcome) {
              console.log('User has seen welcome screen, redirecting to login');
              router.replace('/login');
            } else {
              console.log('User has not seen welcome screen, redirecting to welcome');
              router.replace('/');
            }
          } catch (error) {
            console.error('Error checking welcome status:', error);
            router.replace('/');
          }
        }
      }
    };

    // Add a small delay to prevent rapid navigation changes during logout
    const timeoutId = setTimeout(handleNavigation, 50);
    
    return () => clearTimeout(timeoutId);
  }, [isAuthenticated, segments, user, router]);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <SafeAreaProvider>
      <ErrorBoundary>
        <QueryProvider>
          <AppInitializer>
            <ThemeProvider
              value={colorScheme === "dark" ? DarkTheme : DefaultTheme}
            >
              <Stack screenOptions={{
                animation: 'slide_from_right',
                gestureEnabled: true,
              }}>
                <Stack.Screen name="index" options={{ headerShown: false }} />
                <Stack.Screen name="login" options={{ headerShown: false }} />
                <Stack.Screen
                  name="preferences"
                  options={{ headerShown: false }}
                />
                <Stack.Screen name="dashboard" options={{ headerShown: false }} />
                <Stack.Screen
                  name="listing-details"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="application"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="contract-review"
                  options={{ headerShown: false }}
                />
                <Stack.Screen name="profile" options={{ headerShown: false }} />
                <Stack.Screen
                  name="property-owner"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="autonomous-settings"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="autonomous-status"
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="safety-controls"
                  options={{ headerShown: false }}
                />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
            </ThemeProvider>
          </AppInitializer>
        </QueryProvider>
      </ErrorBoundary>
    </SafeAreaProvider>
  );
}