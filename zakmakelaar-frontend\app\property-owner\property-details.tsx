import React, { useEffect, useState, useCallback, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Alert, ActivityIndicator, Dimensions, FlatList } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { propertyOwnerService } from '../../services/propertyOwnerService';

const { width: screenWidth } = Dimensions.get('window');


// Types
interface PropertyDetails {
  _id: string;
  title: string;
  description: string;
  address: {
    street: string;
    houseNumber: string;
    postalCode: string;
    city: string;
    province?: string;
    country: string;
  };
  propertyType: string;
  size: number;
  rooms: number;
  bedrooms: number;
  bathrooms: number;
  rent: {
    amount: number;
    currency: string;
    deposit: number;
    additionalCosts: {
      utilities: number;
      serviceCharges: number;
      parking: number;
      other: number;
    };
  };
  features: {
    furnished: boolean;
    interior: string;
    parking: boolean;
    balcony: boolean;
    garden: boolean;
    terrace: boolean;
    elevator: boolean;
    energyLabel: string;
  };
  policies: {
    petsAllowed: boolean;
    smokingAllowed: boolean;
    studentsAllowed: boolean;
    expatFriendly: boolean;
    minimumIncome: number;
    maximumOccupants: number;
  };
  status: string;
  images: Array<{
    url: string;
    caption?: string;
    isPrimary: boolean;
  }>;
  metrics: {
    views: number;
    applications: number;
    viewings: number;
  };
}
export default function PropertyDetailsScreen() {
  const router = useRouter();
  const { propertyId } = useLocalSearchParams<{ propertyId: string }>();
  const [property, setProperty] = useState<PropertyDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const carouselRef = useRef<FlatList>(null);

  const fetchPropertyDetails = useCallback(async () => {
    if (!propertyId) return;

    setLoading(true);
    setError(null);

    try {
      // Try to fetch from API first, fall back to mock data if it fails
      try {
        const response = await propertyOwnerService.getPropertyDetails(propertyId);
        if (response.success && response.data) {
          setProperty(response.data as PropertyDetails);
          setLoading(false);
          return;
        }
      } catch (apiError) {
        console.log('API call failed, using mock data:', apiError);
      }

      // Mock data fallback for demonstration
      setTimeout(() => {
        const mockProperty: PropertyDetails = {
          _id: propertyId,
          title: 'Modern Apartment in Utrecht',
          description: 'Beautiful modern apartment in the heart of Utrecht with excellent public transport connections. This property features high-quality finishes, large windows with plenty of natural light, and a modern kitchen with all appliances included.',
          address: {
            street: 'Main Street',
            houseNumber: '123',
            postalCode: '3511AB',
            city: 'Utrecht',
            province: 'Utrecht',
            country: 'Netherlands'
          },
          propertyType: 'apartment',
          size: 75,
          rooms: 3,
          bedrooms: 2,
          bathrooms: 1,
          rent: {
            amount: 1200,
            currency: 'EUR',
            deposit: 2400,
            additionalCosts: {
              utilities: 150,
              serviceCharges: 50,
              parking: 0,
              other: 0
            }
          },
          features: {
            furnished: false,
            interior: 'gestoffeerd',
            parking: false,
            balcony: true,
            garden: false,
            terrace: false,
            elevator: true,
            energyLabel: 'B'
          },
          policies: {
            petsAllowed: false,
            smokingAllowed: false,
            studentsAllowed: true,
            expatFriendly: true,
            minimumIncome: 3600,
            maximumOccupants: 2
          },
          status: 'active',
          images: [
            {
              url: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
              caption: 'Living room',
              isPrimary: true
            },
            {
              url: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
              caption: 'Kitchen',
              isPrimary: false
            },
            {
              url: 'https://images.unsplash.com/photo-1540518614846-7eded47ee3b5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
              caption: 'Bedroom',
              isPrimary: false
            },
            {
              url: 'https://images.unsplash.com/photo-1552321554-5fefe8c9ef14?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
              caption: 'Bathroom',
              isPrimary: false
            },
            {
              url: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
              caption: 'Balcony view',
              isPrimary: false
            }
          ],
          metrics: {
            views: 45,
            applications: 3,
            viewings: 2
          }
        };

        setProperty(mockProperty);
        setLoading(false);
      }, 1000);

    } catch (err: any) {
      setError(err.message || 'Failed to fetch property details');
      setLoading(false);
    }
  }, [propertyId]);

  useEffect(() => {
    if (propertyId) {
      fetchPropertyDetails();
    }
  }, [propertyId, fetchPropertyDetails]);

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('nl-NL', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#10b981';
      case 'rented': return '#4361ee';
      case 'maintenance': return '#f59e0b';
      case 'draft': return '#6b7280';
      case 'inactive': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const handleEdit = () => {
    Alert.alert(
      'Edit Property',
      `Property ID: ${propertyId}. Navigate to edit screen?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Yes',
          onPress: () => {
            if (propertyId) {
              router.push(`/property-owner/edit-property?propertyId=${propertyId}`);
            }
          }
        }
      ]
    );
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Property',
      'Are you sure you want to delete this property? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete', style: 'destructive', onPress: () => {
            Alert.alert('Coming Soon', 'Delete functionality will be available soon!');
          }
        }
      ]
    );
  };

  // Carousel functions
  const onViewableItemsChanged = useCallback(({ viewableItems }: any) => {
    if (viewableItems.length > 0) {
      setCurrentImageIndex(viewableItems[0].index || 0);
    }
  }, []);

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50,
  };

  const goToNextImage = () => {
    if (property?.images && currentImageIndex < property.images.length - 1) {
      const nextIndex = currentImageIndex + 1;
      carouselRef.current?.scrollToIndex({ index: nextIndex, animated: true });
      setCurrentImageIndex(nextIndex);
    }
  };

  const goToPrevImage = () => {
    if (currentImageIndex > 0) {
      const prevIndex = currentImageIndex - 1;
      carouselRef.current?.scrollToIndex({ index: prevIndex, animated: true });
      setCurrentImageIndex(prevIndex);
    }
  };

  const renderImageItem = ({ item, index }: { item: any; index: number }) => (
    <View style={styles.carouselImageContainer}>
      <Image
        source={{ uri: item.url || 'https://via.placeholder.com/400x250?text=No+Image' }}
        style={styles.carouselImage}
        resizeMode="cover"
      />
      {item.caption && (
        <View style={styles.captionOverlay}>
          <Text style={styles.captionText}>{item.caption}</Text>
        </View>
      )}
    </View>
  );

  const renderCarousel = () => {
    const images = property?.images || [];
    
    if (images.length === 0) {
      return (
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: 'https://via.placeholder.com/400x250?text=No+Image' }}
            style={styles.propertyImage}
            resizeMode="cover"
          />
        </View>
      );
    }

    if (images.length === 1) {
      return (
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: images[0].url }}
            style={styles.propertyImage}
            resizeMode="cover"
          />
          {images[0].caption && (
            <View style={styles.captionOverlay}>
              <Text style={styles.captionText}>{images[0].caption}</Text>
            </View>
          )}
        </View>
      );
    }

    return (
      <View style={styles.carouselContainer}>
        <FlatList
          ref={carouselRef}
          data={images}
          renderItem={renderImageItem}
          keyExtractor={(item, index) => index.toString()}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
          getItemLayout={(data, index) => ({
            length: screenWidth,
            offset: screenWidth * index,
            index,
          })}
        />
        
        {/* Navigation Arrows */}
        {images.length > 1 && (
          <>
            {currentImageIndex > 0 && (
              <TouchableOpacity style={styles.prevButton} onPress={goToPrevImage}>
                <Ionicons name="chevron-back" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            )}
            
            {currentImageIndex < images.length - 1 && (
              <TouchableOpacity style={styles.nextButton} onPress={goToNextImage}>
                <Ionicons name="chevron-forward" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            )}
          </>
        )}
        
        {/* Pagination Dots */}
        {images.length > 1 && (
          <View style={styles.paginationContainer}>
            {images.map((_, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.paginationDot,
                  index === currentImageIndex && styles.paginationDotActive,
                ]}
                onPress={() => {
                  carouselRef.current?.scrollToIndex({ index, animated: true });
                  setCurrentImageIndex(index);
                }}
              />
            ))}
          </View>
        )}
        
        {/* Image Counter */}
        <View style={styles.imageCounter}>
          <Text style={styles.imageCounterText}>
            {currentImageIndex + 1} / {images.length}
          </Text>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#007AFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Property Details</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading property details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !property) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#007AFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Property Details</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error || 'Property not found'}</Text>
          <TouchableOpacity onPress={fetchPropertyDetails} style={styles.retryButton}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Property Details</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={handleEdit} style={styles.actionButton}>
            <Ionicons name="create-outline" size={24} color="#007AFF" />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleDelete} style={styles.actionButton}>
            <Ionicons name="trash-outline" size={24} color="#FF3B30" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Property Images Carousel */}
        <View style={styles.imageSection}>
          {renderCarousel()}
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(property.status) }]}>
            <Text style={styles.statusText}>{property.status.toUpperCase()}</Text>
          </View>
        </View>

        {/* Basic Info */}
        <View style={styles.section}>
          <Text style={styles.propertyTitle}>{property.title}</Text>
          <Text style={styles.propertyAddress}>
            {property.address?.street} {property.address?.houseNumber}, {property.address?.city}
          </Text>
          <Text style={styles.propertyPrice}>{formatPrice(property.rent?.amount || 0)}/month</Text>
        </View>

        {/* Quick Stats */}
        <View style={styles.section}>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Ionicons name="resize-outline" size={20} color="#666" />
              <Text style={styles.statText}>{property.size || 0} m²</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="bed-outline" size={20} color="#666" />
              <Text style={styles.statText}>{property.bedrooms || 0} bed</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="water-outline" size={20} color="#666" />
              <Text style={styles.statText}>{property.bathrooms || 0} bath</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="home-outline" size={20} color="#666" />
              <Text style={styles.statText}>{property.rooms || 0} rooms</Text>
            </View>
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.description}>{property.description}</Text>
        </View>
        {/* Rental Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Rental Information</Text>
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Monthly Rent</Text>
              <Text style={styles.infoValue}>{formatPrice(property.rent?.amount || 0)}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Deposit</Text>
              <Text style={styles.infoValue}>{formatPrice(property.rent?.deposit || 0)}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Utilities</Text>
              <Text style={styles.infoValue}>{formatPrice(property.rent?.additionalCosts?.utilities || 0)}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Service Charges</Text>
              <Text style={styles.infoValue}>{formatPrice(property.rent?.additionalCosts?.serviceCharges || 0)}</Text>
            </View>
          </View>
        </View>

        {/* Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Features</Text>
          <View style={styles.featuresGrid}>
            <View style={styles.featureItem}>
              <Ionicons
                name={property.features?.furnished ? "checkmark-circle" : "close-circle"}
                size={20}
                color={property.features?.furnished ? "#10b981" : "#ef4444"}
              />
              <Text style={styles.featureText}>Furnished</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons
                name={property.features?.parking ? "checkmark-circle" : "close-circle"}
                size={20}
                color={property.features?.parking ? "#10b981" : "#ef4444"}
              />
              <Text style={styles.featureText}>Parking</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons
                name={property.features?.balcony ? "checkmark-circle" : "close-circle"}
                size={20}
                color={property.features?.balcony ? "#10b981" : "#ef4444"}
              />
              <Text style={styles.featureText}>Balcony</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons
                name={property.features?.garden ? "checkmark-circle" : "close-circle"}
                size={20}
                color={property.features?.garden ? "#10b981" : "#ef4444"}
              />
              <Text style={styles.featureText}>Garden</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons
                name={property.features?.elevator ? "checkmark-circle" : "close-circle"}
                size={20}
                color={property.features?.elevator ? "#10b981" : "#ef4444"}
              />
              <Text style={styles.featureText}>Elevator</Text>
            </View>
            <View style={styles.featureItem}>
              <Text style={styles.energyLabel}>Energy: {property.features?.energyLabel || 'N/A'}</Text>
            </View>
          </View>
        </View>
        {/* Policies */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Rental Policies</Text>
          <View style={styles.policiesGrid}>
            <View style={styles.policyItem}>
              <Ionicons
                name={property.policies?.petsAllowed ? "checkmark-circle" : "close-circle"}
                size={20}
                color={property.policies?.petsAllowed ? "#10b981" : "#ef4444"}
              />
              <Text style={styles.policyText}>Pets Allowed</Text>
            </View>
            <View style={styles.policyItem}>
              <Ionicons
                name={property.policies?.smokingAllowed ? "checkmark-circle" : "close-circle"}
                size={20}
                color={property.policies?.smokingAllowed ? "#10b981" : "#ef4444"}
              />
              <Text style={styles.policyText}>Smoking Allowed</Text>
            </View>
            <View style={styles.policyItem}>
              <Ionicons
                name={property.policies?.studentsAllowed ? "checkmark-circle" : "close-circle"}
                size={20}
                color={property.policies?.studentsAllowed ? "#10b981" : "#ef4444"}
              />
              <Text style={styles.policyText}>Students Welcome</Text>
            </View>
            <View style={styles.policyItem}>
              <Ionicons
                name={property.policies?.expatFriendly ? "checkmark-circle" : "close-circle"}
                size={20}
                color={property.policies?.expatFriendly ? "#10b981" : "#ef4444"}
              />
              <Text style={styles.policyText}>Expat Friendly</Text>
            </View>
          </View>
          <View style={styles.policyDetails}>
            <Text style={styles.policyDetailText}>
              Minimum Income: {formatPrice(property.policies?.minimumIncome || 0)}/month
            </Text>
            <Text style={styles.policyDetailText}>
              Maximum Occupants: {property.policies?.maximumOccupants || 0}
            </Text>
          </View>
        </View>

        {/* Performance Metrics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Performance</Text>
          <View style={styles.metricsRow}>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>{property.metrics?.views || 0}</Text>
              <Text style={styles.metricLabel}>Views</Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>{property.metrics?.applications || 0}</Text>
              <Text style={styles.metricLabel}>Applications</Text>
            </View>
            <View style={styles.metricItem}>
              <Text style={styles.metricValue}>{property.metrics?.viewings || 0}</Text>
              <Text style={styles.metricLabel}>Viewings</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
  },
  placeholder: {
    width: 80,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  imageSection: {
    position: 'relative',
    height: 250,
  },
  imageContainer: {
    position: 'relative',
    height: 250,
  },
  propertyImage: {
    width: '100%',
    height: '100%',
  },
  // Carousel styles
  carouselContainer: {
    height: 250,
    position: 'relative',
  },
  carouselImageContainer: {
    width: screenWidth,
    height: 250,
    position: 'relative',
  },
  carouselImage: {
    width: '100%',
    height: '100%',
  },
  captionOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  captionText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  prevButton: {
    position: 'absolute',
    left: 16,
    top: '50%',
    transform: [{ translateY: -20 }],
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2,
  },
  nextButton: {
    position: 'absolute',
    right: 16,
    top: '50%',
    transform: [{ translateY: -20 }],
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 2,
  },
  paginationContainer: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: '#FFFFFF',
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  imageCounter: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 2,
  },
  imageCounterText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  statusBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginBottom: 12,
    padding: 16,
  },
  propertyTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: 8,
  },
  propertyAddress: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 8,
  },
  propertyPrice: {
    fontSize: 20,
    fontWeight: '600',
    color: '#007AFF',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statText: {
    fontSize: 14,
    color: '#666666',
    marginTop: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  infoItem: {
    flex: 1,
    minWidth: '45%',
  },
  infoLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: '45%',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#333333',
    marginLeft: 8,
  },
  energyLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
    backgroundColor: '#F0F8FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  policiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  policyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: '45%',
    marginBottom: 8,
  },
  policyText: {
    fontSize: 14,
    color: '#333333',
    marginLeft: 8,
  },
  policyDetails: {
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
    paddingTop: 12,
  },
  policyDetailText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  metricsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  metricItem: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#007AFF',
  },
  metricLabel: {
    fontSize: 14,
    color: '#666666',
    marginTop: 4,
  },
});