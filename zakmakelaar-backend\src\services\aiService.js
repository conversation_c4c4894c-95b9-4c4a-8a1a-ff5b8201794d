const OpenAI = require("openai");
const config = require("../config/config");
const { logHelpers } = require("./logger");

class AIService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: config.openRouter.apiKey,
      baseURL: config.openRouter.baseURL,
    });
    this.defaultModel = config.openRouter.defaultModel;
    this.maxTokens = config.openRouter.maxTokens;
    this.temperature = config.openRouter.temperature;
  }

  /**
   * Helper function to extract JSON from AI response
   */
  extractJSONFromResponse(response) {
    try {
      // First, try to parse as direct JSON
      return JSON.parse(response);
    } catch (error) {
      // If that fails, try to extract JSO<PERSON> from markdown code blocks
      const jsonMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[1]);
        } catch (parseError) {
          console.error("Failed to parse JSON from markdown:", parseError);
          throw new Error("Invalid JSON response from AI");
        }
      }

      // If no markdown blocks, try to find JSON in the text
      const jsonRegex = /\{[\s\S]*\}/;
      const match = response.match(jsonRegex);
      if (match) {
        try {
          return JSON.parse(match[0]);
        } catch (parseError) {
          console.error("Failed to parse JSON from text:", parseError);
          throw new Error("Invalid JSON response from AI");
        }
      }

      throw new Error("No valid JSON found in AI response");
    }
  }

  /**
   * AI-powered listing matching based on user preferences
   */
  async matchListingToUser(listing, userPreferences) {
    try {
      const prompt = this.buildMatchingPrompt(listing, userPreferences);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.matching,
        messages: [
          {
            role: "system",
            content:
              "You are an expert real estate agent specializing in Dutch rental properties. Analyze listings against user preferences and provide a detailed match score and reasoning. Always respond with valid JSON only, no markdown formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: this.maxTokens,
        temperature: 0.3, // Lower temperature for more consistent matching
      });

      const result = this.extractJSONFromResponse(
        response.choices[0].message.content
      );
      logHelpers.logAiOperation("listing_matching", "success", result.score);

      return result;
    } catch (error) {
      logHelpers.logAiOperation("listing_matching", "error", error.message);
      throw error;
    }
  }

  /**
   * Contract analysis and legal guidance
   */
  async analyzeContract(contractText, language = "dutch") {
    try {
      const prompt = this.buildContractAnalysisPrompt(contractText, language);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.analysis,
        messages: [
          {
            role: "system",
            content:
              "You are a Dutch real estate lawyer specializing in rental contracts. Analyze contracts for legal compliance, potential issues, and provide clear explanations. Always respond with valid JSON only, no markdown formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: this.maxTokens,
        temperature: 0.2, // Very low temperature for legal analysis
      });

      const result = this.extractJSONFromResponse(
        response.choices[0].message.content
      );
      logHelpers.logAiOperation(
        "contract_analysis",
        "success",
        result.riskLevel
      );

      return result;
    } catch (error) {
      logHelpers.logAiOperation("contract_analysis", "error", error.message);
      throw error;
    }
  }

  /**
   * Generate personalized application messages
   */
  async generateApplicationMessage(
    listing,
    userProfile,
    template = "professional"
  ) {
    try {
      // Check if API key is configured
      if (!config.openRouter.apiKey) {
        console.error("AI API key not configured");
        throw new Error("AI service not configured. Please set OPENROUTER_API_KEY or OPENAI_API_KEY environment variable.");
      }

      const prompt = this.buildApplicationPrompt(
        listing,
        userProfile,
        template
      );

      console.log("Generating application with model:", config.openRouter.models.analysis);
      console.log("Using API base URL:", config.openRouter.baseURL);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.analysis,
        messages: [
          {
            role: "system",
            content:
              "You are a professional real estate assistant. Generate personalized, compelling application messages for rental properties that highlight the applicant's strengths and suitability. Respond with the application message only, no JSON formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 1000,
        temperature: 0.7,
      });

      const message = response.choices[0].message.content;
      logHelpers.logAiOperation("application_generation", "success", template);

      return {
        message,
        subject: `Application for ${listing.title}`,
        template,
        personalizedElements: ["Property-specific details", "User profile match"],
        tips: ["Follow up within 24 hours", "Be prepared for viewing"],
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error("AI Service Error:", error.message);
      logHelpers.logAiOperation(
        "application_generation",
        "error",
        error.message
      );
      
      // Provide more specific error messages
      if (error.message.includes("API key")) {
        throw new Error("AI service configuration error: API key not set");
      } else if (error.message.includes("model")) {
        throw new Error("AI service error: Invalid model configuration");
      } else if (error.message.includes("network") || error.message.includes("fetch")) {
        throw new Error("AI service error: Network connection failed");
      } else {
        throw new Error(`AI service error: ${error.message}`);
      }
    }
  }

  /**
   * Market analysis and price prediction
   */
  async analyzeMarketTrends(location, propertyType, historicalData) {
    try {
      const prompt = this.buildMarketAnalysisPrompt(
        location,
        propertyType,
        historicalData
      );

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.analysis,
        messages: [
          {
            role: "system",
            content:
              "You are a Dutch real estate market analyst. Analyze market trends, provide price predictions, and offer insights about rental market conditions. Always respond with valid JSON only, no markdown formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: this.maxTokens,
        temperature: 0.4,
      });

      const result = this.extractJSONFromResponse(
        response.choices[0].message.content
      );
      logHelpers.logAiOperation("market_analysis", "success", location);

      return result;
    } catch (error) {
      logHelpers.logAiOperation("market_analysis", "error", error.message);
      throw error;
    }
  }

  /**
   * Smart listing summarization
   */
  async summarizeListing(listing, language = "english") {
    try {
      const prompt = this.buildSummarizationPrompt(listing, language);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.summarization,
        messages: [
          {
            role: "system",
            content:
              "You are a real estate expert. Create concise, informative summaries of rental listings highlighting key features and benefits. Respond with the summary only, no JSON formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 500,
        temperature: 0.5,
      });

      const summary = response.choices[0].message.content;
      logHelpers.logAiOperation("listing_summarization", "success", language);

      return {
        summary,
        language,
        generatedAt: new Date(),
      };
    } catch (error) {
      logHelpers.logAiOperation(
        "listing_summarization",
        "error",
        error.message
      );
      throw error;
    }
  }

  /**
   * Translate listing content
   */
  async translateContent(content, fromLanguage, toLanguage) {
    try {
      const prompt = this.buildTranslationPrompt(
        content,
        fromLanguage,
        toLanguage
      );

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.translation,
        messages: [
          {
            role: "system",
            content:
              "You are a professional translator specializing in real estate terminology. Translate content accurately while preserving technical terms and cultural context. Respond with the translation only, no JSON formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: this.maxTokens,
        temperature: 0.3,
      });

      const translation = response.choices[0].message.content;
      logHelpers.logAiOperation(
        "content_translation",
        "success",
        `${fromLanguage}-${toLanguage}`
      );

      return {
        original: content,
        translation,
        fromLanguage,
        toLanguage,
        translatedAt: new Date(),
      };
    } catch (error) {
      logHelpers.logAiOperation("content_translation", "error", error.message);
      throw error;
    }
  }

  // Private helper methods for building prompts
  buildMatchingPrompt(listing, userPreferences) {
    return `
    Analyze this rental listing against user preferences and provide a detailed match assessment.

    LISTING:
    - Title: ${listing.title || "No title"}
    - Price: ${listing.price || "Not specified"}
    - Location: ${listing.location || "Not specified"}
    - Size: ${listing.size || "Not specified"}
    - Rooms: ${listing.rooms || "Not specified"}
    - Property Type: ${listing.propertyType || "Not specified"}
    - Interior: ${listing.interior || "Not specified"}

    USER PREFERENCES:
    - Preferred Locations: ${userPreferences.preferredLocations ? userPreferences.preferredLocations.join(", ") : "Any"}
    - Budget Range: €${userPreferences.minPrice || 0} - €${userPreferences.maxPrice || "No limit"}
    - Preferred Rooms: ${userPreferences.minRooms || 0} - ${userPreferences.maxRooms || "Any"}
    - Property Types: ${userPreferences.propertyTypes ? userPreferences.propertyTypes.join(", ") : "Any"}

    Please provide a JSON response with:
    {
      "score": 0-100,
      "matchReasoning": "Detailed explanation of why this listing matches or doesn't match",
      "keyHighlights": ["List of key features that match preferences"],
      "potentialConcerns": ["Any potential issues or concerns"],
      "recommendation": "strong_match|good_match|moderate_match|poor_match"
    }
    `;
  }

  buildContractAnalysisPrompt(contractText, language) {
    return `
    Analyze this Dutch rental contract for legal compliance and potential issues.

    CONTRACT TEXT:
    ${contractText}

    Please provide a JSON response with:
    {
      "riskLevel": "low|medium|high",
      "complianceScore": 0-100,
      "keyClauses": ["Important clauses identified"],
      "potentialIssues": ["List of potential legal issues"],
      "recommendations": ["Suggestions for improvement"],
      "summary": "Brief summary in ${language}",
      "legalAdvice": "Professional legal advice"
    }
    `;
  }

  buildApplicationPrompt(listing, userProfile, template) {
    return `
    Generate a ${template} application message for this rental property.

    PROPERTY:
    - Title: ${listing.title}
    - Location: ${listing.location}
    - Price: ${listing.price}

    APPLICANT PROFILE:
    - Name: ${userProfile.name}
    - Income: ${userProfile.income}
    - Occupation: ${userProfile.occupation || "Professional"}

    Generate a compelling, professional application message that highlights the applicant's suitability for this property.
    `;
  }

  buildMarketAnalysisPrompt(location, propertyType, historicalData) {
    return `
    Analyze the rental market trends for ${propertyType} in ${location}.

    HISTORICAL DATA:
    ${JSON.stringify(historicalData)}

    Please provide a JSON response with:
    {
      "marketTrend": "increasing|stable|decreasing",
      "pricePrediction": "Expected price movement",
      "demandLevel": "high|medium|low",
      "keyInsights": ["Important market insights"],
      "recommendations": ["Recommendations for renters"],
      "confidenceScore": 0-100
    }
    `;
  }

  buildSummarizationPrompt(listing, language) {
    return `
    Create a concise summary of this rental listing in ${language}.

    LISTING DETAILS:
    - Title: ${listing.title}
    - Price: ${listing.price}
    - Location: ${listing.location}
    - Size: ${listing.size}
    - Rooms: ${listing.rooms}
    - Property Type: ${listing.propertyType}

    Provide a 2-3 sentence summary highlighting the key features and benefits.
    `;
  }

  /**
   * Get AI-powered property matches for user preferences
   */
  async getPropertyMatches(matchingRequest) {
    try {
      const { userProfile, preferences, listings, maxResults = 50 } = matchingRequest;
      
      // If no listings provided, return empty result
      if (!listings || listings.length === 0) {
        return {
          matches: [],
          totalAnalyzed: 0,
          averageScore: 0,
          recommendations: ["No properties available matching your criteria. Try adjusting your preferences."]
        };
      }

      // Simple scoring algorithm based on preferences
      const scoredMatches = listings.map(listing => {
        let score = 0;
        const reasons = [];
        const pros = [];
        const cons = [];

        // Location matching
        if (preferences.preferredLocations && preferences.preferredLocations.length > 0) {
          const locationMatch = preferences.preferredLocations.some(prefLocation => 
            listing.location && listing.location.toLowerCase().includes(prefLocation.toLowerCase())
          );
          if (locationMatch) {
            score += 30;
            reasons.push("Location matches your preferences");
            pros.push("Preferred location");
          } else {
            cons.push("Location not in preferred areas");
          }
        }

        // Property type matching
        if (preferences.propertyTypes && preferences.propertyTypes.length > 0) {
          const typeMatch = preferences.propertyTypes.some(prefType => 
            listing.propertyType && listing.propertyType.toLowerCase().includes(prefType.toLowerCase())
          );
          if (typeMatch) {
            score += 25;
            reasons.push("Property type matches your preferences");
            pros.push("Preferred property type");
          } else {
            cons.push("Property type not preferred");
          }
        }

        // Price matching
        if (listing.price) {
          const priceMatch = listing.price.match(/[\d,]+/);
          if (priceMatch) {
            const listingPrice = parseInt(priceMatch[0].replace(/,/g, ''));
            if (listingPrice >= (preferences.minPrice || 0) && listingPrice <= (preferences.maxPrice || 10000)) {
              score += 20;
              reasons.push("Price within your budget");
              pros.push("Affordable price");
            } else if (listingPrice > (preferences.maxPrice || 10000)) {
              cons.push("Above budget");
            } else {
              cons.push("Below minimum price");
            }
          }
        }

        // Room matching
        if (listing.rooms) {
          const roomsMatch = listing.rooms.match(/\d+/);
          if (roomsMatch) {
            const listingRooms = parseInt(roomsMatch[0]);
            if (listingRooms >= (preferences.minRooms || 1) && listingRooms <= (preferences.maxRooms || 10)) {
              score += 15;
              reasons.push("Number of rooms matches your needs");
              pros.push("Right number of rooms");
            } else {
              cons.push("Room count doesn't match preferences");
            }
          }
        }

        // Amenities matching (if available)
        if (preferences.amenities && preferences.amenities.length > 0 && listing.description) {
          const matchedAmenities = preferences.amenities.filter(amenity => 
            listing.description.toLowerCase().includes(amenity.toLowerCase())
          );
          if (matchedAmenities.length > 0) {
            score += matchedAmenities.length * 2;
            reasons.push(`Has ${matchedAmenities.length} preferred amenities`);
            pros.push(`Includes: ${matchedAmenities.join(', ')}`);
          }
        }

        // Add some randomness to avoid identical scores
        score += Math.random() * 5;

        return {
          listing,
          score: Math.round(score),
          reasons,
          pros,
          cons
        };
      });

      // Sort by score and limit results
      const sortedMatches = scoredMatches
        .sort((a, b) => b.score - a.score)
        .slice(0, maxResults);

      // Calculate average score
      const averageScore = sortedMatches.length > 0 
        ? Math.round(sortedMatches.reduce((sum, match) => sum + match.score, 0) / sortedMatches.length)
        : 0;

      // Generate recommendations
      const recommendations = [];
      if (sortedMatches.length === 0) {
        recommendations.push("No properties match your current criteria. Consider adjusting your preferences.");
      } else if (averageScore < 30) {
        recommendations.push("Consider broadening your search criteria for better matches.");
      } else if (averageScore > 70) {
        recommendations.push("Great matches found! These properties align well with your preferences.");
      } else {
        recommendations.push("Some good matches found. Review the pros and cons for each property.");
      }

      return {
        matches: sortedMatches,
        totalAnalyzed: listings.length,
        averageScore,
        recommendations
      };

    } catch (error) {
      console.error('Error in getPropertyMatches:', error);
      throw error;
    }
  }

  buildTranslationPrompt(content, fromLanguage, toLanguage) {
    return `
    Translate the following ${fromLanguage} real estate content to ${toLanguage}:

    CONTENT:
    ${content}

    Maintain professional real estate terminology and cultural context in the translation.
    `;
  }

  // ===== AUTO-APPLICATION SPECIFIC METHODS =====

  /**
   * Generate personalized application letter for auto-application
   * @param {Object} listing - Property listing data
   * @param {Object} userProfile - User profile with personal information
   * @param {Object} settings - Auto-application settings including template type
   * @param {string} language - Language for the application (dutch/english)
   * @returns {Object} Generated application content
   */
  async generateAutoApplicationLetter(listing, userProfile, settings, language = 'dutch') {
    try {
      const template = settings.applicationTemplate || 'professional';
      const prompt = this.buildAutoApplicationPrompt(listing, userProfile, template, language);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.analysis,
        messages: [
          {
            role: "system",
            content: this.getAutoApplicationSystemPrompt(template, language)
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 1200,
        temperature: 0.7,
      });

      const content = response.choices[0].message.content;
      const result = this.parseAutoApplicationResponse(content, listing, template, language);
      
      logHelpers.logAiOperation("auto_application_generation", "success", template);
      return result;

    } catch (error) {
      logHelpers.logAiOperation("auto_application_generation", "error", error.message);
      throw new Error(`Auto-application generation failed: ${error.message}`);
    }
  }

  /**
   * Generate property-specific customized content
   * @param {Object} listing - Property listing data
   * @param {Object} userProfile - User profile data
   * @param {Array} propertyFeatures - Specific features to highlight
   * @param {string} language - Language for customization
   * @returns {Object} Customized content elements
   */
  async generatePropertySpecificContent(listing, userProfile, propertyFeatures = [], language = 'dutch') {
    try {
      const prompt = this.buildPropertyCustomizationPrompt(listing, userProfile, propertyFeatures, language);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.analysis,
        messages: [
          {
            role: "system",
            content: `You are a real estate application specialist. Generate property-specific content that highlights why the applicant is perfect for this specific property. Respond in ${language} with JSON format.`
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 800,
        temperature: 0.6,
      });

      const result = this.extractJSONFromResponse(response.choices[0].message.content);
      logHelpers.logAiOperation("property_customization", "success", listing.id);
      
      return {
        ...result,
        generatedAt: new Date().toISOString(),
        language
      };

    } catch (error) {
      logHelpers.logAiOperation("property_customization", "error", error.message);
      throw new Error(`Property customization failed: ${error.message}`);
    }
  }

  /**
   * Generate template-based application content
   * @param {string} templateType - Type of template (professional, casual, student, expat)
   * @param {Object} userProfile - User profile data
   * @param {Object} listing - Property listing data
   * @param {string} language - Language for the template
   * @returns {Object} Template-based content
   */
  async generateTemplateBasedContent(templateType, userProfile, listing, language = 'dutch') {
    try {
      const prompt = this.buildTemplatePrompt(templateType, userProfile, listing, language);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.analysis,
        messages: [
          {
            role: "system",
            content: this.getTemplateSystemPrompt(templateType, language)
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 1000,
        temperature: 0.7,
      });

      const content = response.choices[0].message.content;
      logHelpers.logAiOperation("template_generation", "success", templateType);
      
      return {
        content,
        templateType,
        language,
        generatedAt: new Date().toISOString(),
        personalizedElements: this.extractPersonalizedElements(content, userProfile)
      };

    } catch (error) {
      logHelpers.logAiOperation("template_generation", "error", error.message);
      throw new Error(`Template generation failed: ${error.message}`);
    }
  }

  /**
   * Enhance application content with property-specific details
   * @param {string} baseContent - Base application content
   * @param {Object} listing - Property listing data
   * @param {Object} userProfile - User profile data
   * @param {string} language - Language for enhancement
   * @returns {Object} Enhanced content
   */
  async enhanceWithPropertyDetails(baseContent, listing, userProfile, language = 'dutch') {
    try {
      const prompt = this.buildEnhancementPrompt(baseContent, listing, userProfile, language);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.analysis,
        messages: [
          {
            role: "system",
            content: `You are an expert at personalizing rental applications. Enhance the provided content with specific property details and user connections. Respond in ${language}.`
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 1000,
        temperature: 0.6,
      });

      const enhancedContent = response.choices[0].message.content;
      logHelpers.logAiOperation("content_enhancement", "success", listing.id);
      
      return {
        originalContent: baseContent,
        enhancedContent,
        enhancements: this.identifyEnhancements(baseContent, enhancedContent),
        language,
        enhancedAt: new Date().toISOString()
      };

    } catch (error) {
      logHelpers.logAiOperation("content_enhancement", "error", error.message);
      throw new Error(`Content enhancement failed: ${error.message}`);
    }
  }

  /**
   * Generate multi-language application content
   * @param {Object} listing - Property listing data
   * @param {Object} userProfile - User profile data
   * @param {string} templateType - Template type
   * @param {Array} languages - Languages to generate content for
   * @returns {Object} Multi-language content
   */
  async generateMultiLanguageContent(listing, userProfile, templateType, languages = ['dutch', 'english']) {
    try {
      const results = {};
      
      for (const language of languages) {
        const content = await this.generateAutoApplicationLetter(
          listing, 
          userProfile, 
          { applicationTemplate: templateType }, 
          language
        );
        results[language] = content;
      }

      logHelpers.logAiOperation("multilanguage_generation", "success", languages.join(','));
      
      return {
        languages: results,
        primaryLanguage: languages[0],
        generatedAt: new Date().toISOString(),
        templateType
      };

    } catch (error) {
      logHelpers.logAiOperation("multilanguage_generation", "error", error.message);
      throw new Error(`Multi-language generation failed: ${error.message}`);
    }
  }

  // ===== PRIVATE HELPER METHODS FOR AUTO-APPLICATION =====

  /**
   * Build auto-application prompt
   */
  buildAutoApplicationPrompt(listing, userProfile, template, language) {
    const personalInfo = userProfile.personalInfo || {};
    
    return `
    Generate a ${template} rental application letter in ${language} for this property.

    PROPERTY DETAILS:
    - Title: ${listing.title || 'Property'}
    - Location: ${listing.location || 'Not specified'}
    - Price: ${listing.price || 'Not specified'}
    - Size: ${listing.size || 'Not specified'}
    - Rooms: ${listing.rooms || 'Not specified'}
    - Property Type: ${listing.propertyType || 'Not specified'}
    - Description: ${listing.description || 'No description available'}

    APPLICANT INFORMATION:
    - Name: ${personalInfo.fullName || userProfile.name || 'Applicant'}
    - Age: ${personalInfo.dateOfBirth ? this.calculateAge(personalInfo.dateOfBirth) : 'Not specified'}
    - Occupation: ${personalInfo.occupation || 'Professional'}
    - Employer: ${personalInfo.employer || 'Not specified'}
    - Monthly Income: €${personalInfo.monthlyIncome || 'Not specified'}
    - Nationality: ${personalInfo.nationality || 'Not specified'}
    - Move-in Date: ${personalInfo.moveInDate || 'Flexible'}
    - Lease Duration: ${personalInfo.leaseDuration || 'Standard'} months
    - Number of Occupants: ${personalInfo.numberOfOccupants || 1}
    - Has Guarantor: ${personalInfo.hasGuarantor ? 'Yes' : 'No'}

    TEMPLATE STYLE: ${template}
    - Professional: Formal, business-like tone
    - Casual: Friendly, approachable tone
    - Student: Emphasize studies, reliability, future potential
    - Expat: Highlight international background, cultural adaptability

    Generate a compelling application letter that:
    1. Shows genuine interest in this specific property
    2. Highlights relevant applicant strengths
    3. Addresses potential landlord concerns
    4. Maintains appropriate tone for the template style
    5. Is written in fluent ${language}

    Respond with just the letter content, no additional formatting.
    `;
  }

  /**
   * Build property customization prompt
   */
  buildPropertyCustomizationPrompt(listing, userProfile, propertyFeatures, language) {
    return `
    Create property-specific customization elements for this rental application.

    PROPERTY: ${listing.title} in ${listing.location}
    FEATURES TO HIGHLIGHT: ${propertyFeatures.join(', ') || 'General features'}
    APPLICANT: ${userProfile.personalInfo?.fullName || userProfile.name}

    Generate JSON with:
    {
      "propertyConnection": "Why this specific property appeals to the applicant",
      "locationBenefits": "How the location suits the applicant's needs",
      "featureHighlights": ["Specific property features that match applicant needs"],
      "personalizedReasons": ["Personal reasons for choosing this property"],
      "futureVision": "How the applicant envisions living in this property"
    }

    Respond in ${language} with valid JSON only.
    `;
  }

  /**
   * Build template-specific prompt
   */
  buildTemplatePrompt(templateType, userProfile, listing, language) {
    const templateInstructions = {
      professional: 'Formal business tone, emphasize career stability and reliability',
      casual: 'Friendly conversational tone, show personality while remaining respectful',
      student: 'Emphasize academic focus, responsibility, and future potential',
      expat: 'Highlight international experience, cultural adaptability, and integration efforts'
    };

    return `
    Generate a ${templateType} rental application letter in ${language}.

    TEMPLATE GUIDELINES: ${templateInstructions[templateType]}

    PROPERTY: ${listing.title} - ${listing.location}
    APPLICANT: ${userProfile.personalInfo?.fullName || userProfile.name}
    OCCUPATION: ${userProfile.personalInfo?.occupation || 'Professional'}

    Create content that perfectly matches the ${templateType} template style while being genuine and compelling.
    `;
  }

  /**
   * Build content enhancement prompt
   */
  buildEnhancementPrompt(baseContent, listing, userProfile, language) {
    return `
    Enhance this rental application with specific property details and personal connections.

    ORIGINAL CONTENT:
    ${baseContent}

    PROPERTY DETAILS:
    - Title: ${listing.title}
    - Location: ${listing.location}
    - Features: ${listing.description || 'Standard features'}

    APPLICANT PROFILE:
    - Name: ${userProfile.personalInfo?.fullName || userProfile.name}
    - Lifestyle: ${userProfile.personalInfo?.occupation || 'Professional lifestyle'}

    Enhance the content by:
    1. Adding specific property feature mentions
    2. Creating personal connections to the location
    3. Showing detailed knowledge of the property
    4. Maintaining the original tone and style

    Respond with the enhanced content in ${language}.
    `;
  }

  /**
   * Get system prompt for auto-application
   */
  getAutoApplicationSystemPrompt(template, language) {
    const languageInstructions = {
      dutch: 'Write in fluent Dutch with proper grammar and formal/informal tone as appropriate',
      english: 'Write in fluent English with proper grammar and appropriate formality'
    };

    return `You are an expert rental application writer specializing in Dutch real estate market. 
    Generate compelling, personalized application letters that stand out to landlords while remaining genuine and professional.
    
    Template Style: ${template}
    Language: ${languageInstructions[language] || languageInstructions.english}
    
    Focus on:
    - Genuine interest in the specific property
    - Applicant's reliability and suitability
    - Addressing common landlord concerns
    - Professional presentation
    - Cultural appropriateness for Dutch rental market
    
    Respond with the application letter content only, no additional formatting or explanations.`;
  }

  /**
   * Get template-specific system prompt
   */
  getTemplateSystemPrompt(templateType, language) {
    const templatePrompts = {
      professional: `You are writing formal business correspondence for rental applications. Use professional language, emphasize career stability, financial reliability, and long-term commitment.`,
      casual: `You are writing friendly, approachable rental applications. Use conversational tone while maintaining respect and professionalism. Show personality and warmth.`,
      student: `You are writing rental applications for students. Emphasize academic dedication, responsibility, future potential, and understanding of student life balance.`,
      expat: `You are writing rental applications for international residents. Highlight cultural adaptability, international experience, integration efforts, and global perspective.`
    };

    return `${templatePrompts[templateType]} Write in fluent ${language} with appropriate cultural context for the Dutch rental market.`;
  }

  /**
   * Parse auto-application response
   */
  parseAutoApplicationResponse(content, listing, template, language) {
    return {
      subject: `Rental Application - ${listing.title}`,
      message: content.trim(),
      template,
      language,
      personalizedElements: this.extractPersonalizedElements(content, listing),
      generatedAt: new Date().toISOString(),
      wordCount: content.trim().split(/\s+/).length,
      estimatedReadTime: Math.ceil(content.trim().split(/\s+/).length / 200) // minutes
    };
  }

  /**
   * Extract personalized elements from content
   */
  extractPersonalizedElements(content, context) {
    const elements = [];
    
    if (context.title && content.toLowerCase().includes(context.title.toLowerCase())) {
      elements.push('Property title mentioned');
    }
    if (context.location && content.toLowerCase().includes(context.location.toLowerCase())) {
      elements.push('Location referenced');
    }
    if (context.personalInfo?.occupation && content.toLowerCase().includes(context.personalInfo.occupation.toLowerCase())) {
      elements.push('Occupation highlighted');
    }
    if (context.personalInfo?.fullName && content.includes(context.personalInfo.fullName)) {
      elements.push('Personal name included');
    }
    
    return elements;
  }

  /**
   * Identify enhancements made to content
   */
  identifyEnhancements(original, enhanced) {
    const enhancements = [];
    
    if (enhanced.length > original.length * 1.1) {
      enhancements.push('Content expanded with additional details');
    }
    if (enhanced.includes('€') && !original.includes('€')) {
      enhancements.push('Financial details added');
    }
    if (enhanced.split('.').length > original.split('.').length) {
      enhancements.push('Additional sentences for clarity');
    }
    
    return enhancements;
  }

  /**
   * Calculate age from date of birth
   */
  calculateAge(dateOfBirth) {
    if (!dateOfBirth) return 'Not specified';
    
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }
}

module.exports = new AIService();
