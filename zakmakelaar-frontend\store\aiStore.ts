import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  aiService,
  MatchingRequest,
  MatchingResult,
  MatchingResponse,
  ContractAnalysisRequest,
  ContractAnalysisResult,
  ApplicationGenerationRequest,
  ApplicationGenerationResult,
  MarketAnalysisRequest,
  MarketAnalysisResult,
  TranslationRequest,
  TranslationResult,
} from "../services/aiService";
import { Listing } from "../services/listingsService";
import { User, UserPreferences } from "../services/authService";

// Enhanced types for AI Store
export interface PropertyMatch extends MatchingResult {
  id: string;
  timestamp: Date | string; // Can be string after JSON serialization
  viewed: boolean;
  applied: boolean;
  saved: boolean;
}

export interface GeneratedApplication {
  id: string;
  listingId: string;
  propertyTitle: string;
  content: ApplicationGenerationResult;
  status: 'draft' | 'approved' | 'submitted' | 'responded';
  submissionMethod: 'manual' | 'autonomous';
  createdAt: Date | string; // Can be string after JSON serialization
  submittedAt?: Date | string; // Can be string after JSON serialization
  response?: {
    status: 'accepted' | 'rejected' | 'pending';
    message?: string;
    receivedAt: Date | string; // Can be string after JSON serialization
  };
}

export interface ContractAnalysis {
  id: string;
  contractName: string;
  analysis: ContractAnalysisResult;
  createdAt: Date | string; // Can be string after JSON serialization
  listingId?: string;
}

export interface MarketInsights {
  location: string;
  analysis: MarketAnalysisResult;
  lastUpdated: Date | string; // Can be string after JSON serialization
  userSpecific: boolean;
}

export interface AutonomousSettings {
  enabled: boolean;
  autoApplyThreshold: number; // 0-100
  maxApplicationsPerDay: number;
  maxApplicationsPerWeek: number;
  autoApplyPropertyTypes: string[];
  autoApplyMaxPrice: number;
  autoApplyMinMatchScore: number;
  defaultApplicationStyle: 'professional' | 'personal' | 'creative';
  includePersonalTouch: boolean;
  requireConfirmationForExpensive: boolean;
  pauseOnMultipleRejections: boolean;
  maxBudgetOverride: number;
  notifyOnApplication: boolean;
  notifyOnResponse: boolean;
  dailySummary: boolean;
}

export interface AutonomousStatus {
  isActive: boolean;
  currentActivity?: string;
  applicationsToday: number;
  applicationsThisWeek: number;
  lastActivity?: Date | string; // Can be string after JSON serialization
  pausedReason?: string;
  nextScheduledCheck?: Date | string; // Can be string after JSON serialization
}

export interface AutonomousActivity {
  id: string;
  type: 'application_generated' | 'application_submitted' | 'match_found' | 'error' | 'paused' | 'resumed';
  listingId?: string;
  propertyTitle?: string;
  message: string;
  timestamp: Date | string; // Can be string after JSON serialization
  success: boolean;
  details?: any;
}

interface AIState {
  // Property matching state
  matches: PropertyMatch[];
  matchingInProgress: boolean;
  lastMatchUpdate: Date | string | null; // Can be string after JSON serialization
  matchingError: string | null;
  
  // Applications state
  applications: GeneratedApplication[];
  applicationInProgress: boolean;
  applicationError: string | null;
  
  // Contract analysis state
  contractAnalyses: ContractAnalysis[];
  analysisInProgress: boolean;
  analysisError: string | null;
  
  // Market insights state
  marketInsights: MarketInsights[];
  insightsInProgress: boolean;
  insightsError: string | null;
  
  // Autonomous mode state
  autonomousSettings: AutonomousSettings;
  autonomousStatus: AutonomousStatus;
  autonomousActivities: AutonomousActivity[];
  
  // Translation state
  translationCache: Map<string, TranslationResult>;
  translationInProgress: boolean;
  
  // General state
  isLoading: boolean;
  error: string | null;
  
  // Property matching actions
  requestPropertyMatching: (preferences: UserPreferences, user: User) => Promise<PropertyMatch[]>;
  refreshMatches: () => Promise<void>;
  markMatchViewed: (matchId: string) => void;
  markMatchApplied: (matchId: string) => void;
  saveMatch: (matchId: string) => void;
  unsaveMatch: (matchId: string) => void;
  
  // Application generation actions
  generateApplication: (
    listing: Listing,
    user: User,
    style?: 'professional' | 'personal' | 'creative' | 'student' | 'expat',
    customMessage?: string
  ) => Promise<GeneratedApplication>;
  approveApplication: (applicationId: string) => Promise<boolean>;
  submitApplication: (applicationId: string, method: 'manual' | 'autonomous') => Promise<boolean>;
  updateApplicationStatus: (applicationId: string, status: GeneratedApplication['status']) => void;
  deleteApplication: (applicationId: string) => void;
  
  // Contract analysis actions
  analyzeContract: (
    request: ContractAnalysisRequest,
    contractName: string,
    listingId?: string
  ) => Promise<ContractAnalysis>;
  deleteContractAnalysis: (analysisId: string) => void;
  
  // Market insights actions
  getMarketInsights: (
    location: string,
    preferences?: UserPreferences
  ) => Promise<MarketInsights>;
  refreshMarketInsights: (location: string) => Promise<void>;
  
  // Autonomous mode actions
  updateAutonomousSettings: (settings: Partial<AutonomousSettings>) => Promise<boolean>;
  startAutonomousMode: () => Promise<boolean>;
  stopAutonomousMode: () => Promise<boolean>;
  pauseAutonomousMode: (reason: string) => Promise<boolean>;
  resumeAutonomousMode: () => Promise<boolean>;
  getAutonomousStatus: () => AutonomousStatus;
  addAutonomousActivity: (activity: Omit<AutonomousActivity, 'id' | 'timestamp'>) => void;
  clearAutonomousActivities: () => void;
  
  // Safety control methods
  checkDailyLimit: () => boolean;
  checkWeeklyLimit: () => boolean;
  checkBudgetOverride: (propertyPrice: number, userMaxBudget: number) => boolean;
  shouldRequireConfirmation: (propertyPrice: number, userMaxBudget: number) => boolean;
  checkRecentRejections: () => boolean;
  enforceApplicationLimits: () => Promise<boolean>;
  resetDailyCounters: () => void;
  resetWeeklyCounters: () => void;
  
  // Translation actions
  translateText: (text: string, targetLanguage: 'en' | 'nl') => Promise<TranslationResult>;
  clearTranslationCache: () => void;
  
  // Utility actions
  clearError: () => void;
  clearAllData: () => void;
  getMatchById: (matchId: string) => PropertyMatch | undefined;
  getApplicationById: (applicationId: string) => GeneratedApplication | undefined;
  getAnalysisById: (analysisId: string) => ContractAnalysis | undefined;
}

const defaultAutonomousSettings: AutonomousSettings = {
  enabled: false,
  autoApplyThreshold: 80,
  maxApplicationsPerDay: 5,
  maxApplicationsPerWeek: 20,
  autoApplyPropertyTypes: [],
  autoApplyMaxPrice: 2000,
  autoApplyMinMatchScore: 75,
  defaultApplicationStyle: 'professional',
  includePersonalTouch: true,
  requireConfirmationForExpensive: true,
  pauseOnMultipleRejections: true,
  maxBudgetOverride: 10,
  notifyOnApplication: true,
  notifyOnResponse: true,
  dailySummary: true,
};

const defaultAutonomousStatus: AutonomousStatus = {
  isActive: false,
  applicationsToday: 0,
  applicationsThisWeek: 0,
};

export const useAIStore = create<AIState>()(
  persist(
    (set, get) => ({
      // Initial state
      matches: [],
      matchingInProgress: false,
      lastMatchUpdate: null,
      matchingError: null,
      
      applications: [],
      applicationInProgress: false,
      applicationError: null,
      
      contractAnalyses: [],
      analysisInProgress: false,
      analysisError: null,
      
      marketInsights: [],
      insightsInProgress: false,
      insightsError: null,
      
      autonomousSettings: defaultAutonomousSettings,
      autonomousStatus: defaultAutonomousStatus,
      autonomousActivities: [
        {
          id: 'activity_1',
          type: 'application_submitted',
          listingId: '1',
          propertyTitle: 'Modern Apartment in Amsterdam',
          message: 'Application submitted successfully',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          success: true,
        },
        {
          id: 'activity_2',
          type: 'match_found',
          listingId: '2',
          propertyTitle: 'Cozy Studio in Utrecht',
          message: 'New high-match property found (92% match)',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
          success: true,
        },
        {
          id: 'activity_3',
          type: 'application_generated',
          listingId: '3',
          propertyTitle: 'Spacious House in Rotterdam',
          message: 'Application generated and ready for review',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
          success: true,
        },
      ],
      
      translationCache: new Map(),
      translationInProgress: false,
      
      isLoading: false,
      error: null,
      
      // Property matching actions
      requestPropertyMatching: async (preferences: UserPreferences, user: User) => {
        set({ matchingInProgress: true, matchingError: null });
        
        try {
          const request: MatchingRequest = {
            userProfile: user,
            preferences,
            maxResults: 50,
          };
          
          const response = await aiService.getPropertyMatches(request);
          
          if (response.success && response.data) {
            const newMatches: PropertyMatch[] = response.data.matches.map((match, index) => ({
              ...match,
              id: `match_${Date.now()}_${index}`,
              timestamp: new Date(),
              viewed: false,
              applied: false,
              saved: false,
            }));
            
            set({
              matches: newMatches,
              lastMatchUpdate: new Date(),
              matchingInProgress: false,
              matchingError: null,
            });
            
            return newMatches;
          } else {
            const error = response.message || 'Failed to get property matches';
            set({
              matchingError: error,
              matchingInProgress: false,
            });
            throw new Error(error);
          }
        } catch (error: any) {
          // Handle "No suitable match found" as a valid empty result, not an error
          if (error.status === 404 && error.message === 'No suitable match found') {
            set({
              matches: [],
              lastMatchUpdate: new Date(),
              matchingInProgress: false,
              matchingError: null,
            });
            return [];
          }
          
          const errorMessage = error.message || 'Failed to get property matches';
          set({
            matchingError: errorMessage,
            matchingInProgress: false,
          });
          throw error;
        }
      },
      
      refreshMatches: async () => {
        const state = get();
        if (state.lastMatchUpdate) {
          // Re-run matching with last known preferences
          // This would need to be implemented with stored preferences
          console.log('Refreshing matches...');
        }
      },
      
      markMatchViewed: (matchId: string) => {
        set((state) => ({
          matches: state.matches.map((match) =>
            match.id === matchId ? { ...match, viewed: true } : match
          ),
        }));
      },
      
      markMatchApplied: (matchId: string) => {
        set((state) => ({
          matches: state.matches.map((match) =>
            match.id === matchId ? { ...match, applied: true } : match
          ),
        }));
      },
      
      saveMatch: (matchId: string) => {
        set((state) => ({
          matches: state.matches.map((match) =>
            match.id === matchId ? { ...match, saved: true } : match
          ),
        }));
      },
      
      unsaveMatch: (matchId: string) => {
        set((state) => ({
          matches: state.matches.map((match) =>
            match.id === matchId ? { ...match, saved: false } : match
          ),
        }));
      },
      
      // Application generation actions
      generateApplication: async (
        listing: Listing,
        user: User,
        style: 'professional' | 'personal' | 'creative' | 'student' | 'expat' = 'professional',
        customMessage?: string
      ) => {
        set({ applicationInProgress: true, applicationError: null });
        
        try {
          const request: ApplicationGenerationRequest = {
            listing,
            userProfile: user,
            template: style,
            customMessage,
            includeDocuments: true,
          };
          
          const response = await aiService.generateApplication(request);
          
          if (response.success && response.data) {
            const newApplication: GeneratedApplication = {
              id: `app_${Date.now()}`,
              listingId: listing._id,
              propertyTitle: listing.title,
              content: response.data,
              status: 'draft',
              submissionMethod: 'manual',
              createdAt: new Date(),
            };
            
            set((state) => ({
              applications: [...state.applications, newApplication],
              applicationInProgress: false,
              applicationError: null,
            }));
            
            return newApplication;
          } else {
            const error = response.message || 'Failed to generate application';
            set({
              applicationError: error,
              applicationInProgress: false,
            });
            throw new Error(error);
          }
        } catch (error: any) {
          const errorMessage = error.message || 'Failed to generate application';
          set({
            applicationError: errorMessage,
            applicationInProgress: false,
          });
          throw error;
        }
      },
      
      approveApplication: async (applicationId: string) => {
        set((state) => ({
          applications: state.applications.map((app) =>
            app.id === applicationId ? { ...app, status: 'approved' } : app
          ),
        }));
        return true;
      },
      
      submitApplication: async (applicationId: string, method: 'manual' | 'autonomous') => {
        try {
          set((state) => ({
            applications: state.applications.map((app) =>
              app.id === applicationId
                ? {
                    ...app,
                    status: 'submitted',
                    submissionMethod: method,
                    submittedAt: new Date(),
                  }
                : app
            ),
          }));
          
          // Update autonomous status if autonomous submission
          if (method === 'autonomous') {
            const state = get();
            set({
              autonomousStatus: {
                ...state.autonomousStatus,
                applicationsToday: state.autonomousStatus.applicationsToday + 1,
                applicationsThisWeek: state.autonomousStatus.applicationsThisWeek + 1,
                lastActivity: new Date(),
              },
            });
          }
          
          return true;
        } catch (error) {
          console.error('Failed to submit application:', error);
          return false;
        }
      },
      
      updateApplicationStatus: (applicationId: string, status: GeneratedApplication['status']) => {
        set((state) => ({
          applications: state.applications.map((app) =>
            app.id === applicationId ? { ...app, status } : app
          ),
        }));
      },
      
      deleteApplication: (applicationId: string) => {
        set((state) => ({
          applications: state.applications.filter((app) => app.id !== applicationId),
        }));
      },
      
      // Contract analysis actions
      analyzeContract: async (
        request: ContractAnalysisRequest,
        contractName: string,
        listingId?: string
      ) => {
        set({ analysisInProgress: true, analysisError: null });
        
        try {
          const response = await aiService.analyzeContract(request);
          
          if (response.success && response.data) {
            const newAnalysis: ContractAnalysis = {
              id: `analysis_${Date.now()}`,
              contractName,
              analysis: response.data,
              createdAt: new Date(),
              listingId,
            };
            
            set((state) => ({
              contractAnalyses: [...state.contractAnalyses, newAnalysis],
              analysisInProgress: false,
              analysisError: null,
            }));
            
            return newAnalysis;
          } else {
            const error = response.message || 'Failed to analyze contract';
            set({
              analysisError: error,
              analysisInProgress: false,
            });
            throw new Error(error);
          }
        } catch (error: any) {
          const errorMessage = error.message || 'Failed to analyze contract';
          set({
            analysisError: errorMessage,
            analysisInProgress: false,
          });
          throw error;
        }
      },
      
      deleteContractAnalysis: (analysisId: string) => {
        set((state) => ({
          contractAnalyses: state.contractAnalyses.filter((analysis) => analysis.id !== analysisId),
        }));
      },
      
      // Market insights actions
      getMarketInsights: async (location: string, preferences?: UserPreferences) => {
        set({ insightsInProgress: true, insightsError: null });
        
        try {
          const request: MarketAnalysisRequest = {
            location,
            propertyType: preferences?.propertyTypes?.[0],
            priceRange: preferences ? {
              min: preferences.minPrice,
              max: preferences.maxPrice,
            } : undefined,
            timeframe: '3months',
          };
          
          const response = await aiService.getMarketAnalysis(request);
          console.log('AIStore - Market insights response:', response);
          
          if (response.success && response.data) {
            const newInsights: MarketInsights = {
              location,
              analysis: response.data,
              lastUpdated: new Date(),
              userSpecific: !!preferences,
            };
            
            set((state) => ({
              marketInsights: [
                ...state.marketInsights.filter((insight) => insight.location !== location),
                newInsights,
              ],
              insightsInProgress: false,
              insightsError: null,
            }));
            
            return newInsights;
          } else {
            const error = response.message || 'Failed to get market insights';
            set({
              insightsError: error,
              insightsInProgress: false,
            });
            throw new Error(error);
          }
        } catch (error: any) {
          const errorMessage = error.message || 'Failed to get market insights';
          set({
            insightsError: errorMessage,
            insightsInProgress: false,
          });
          throw error;
        }
      },
      
      refreshMarketInsights: async (location: string) => {
        const state = get();
        const existingInsights = state.marketInsights.find((insight) => insight.location === location);
        
        if (existingInsights) {
          await get().getMarketInsights(location);
        }
      },
      
      // Autonomous mode actions
      updateAutonomousSettings: async (settings: Partial<AutonomousSettings>) => {
        try {
          set((state) => ({
            autonomousSettings: {
              ...state.autonomousSettings,
              ...settings,
            },
          }));
          return true;
        } catch (error) {
          console.error('Failed to update autonomous settings:', error);
          return false;
        }
      },
      
      startAutonomousMode: async () => {
        try {
          set((state) => ({
            autonomousSettings: {
              ...state.autonomousSettings,
              enabled: true,
            },
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive: true,
              currentActivity: 'Starting autonomous mode...',
            },
          }));
          
          get().addAutonomousActivity({
            type: 'resumed',
            message: 'Autonomous mode started',
            success: true,
          });
          
          return true;
        } catch (error) {
          console.error('Failed to start autonomous mode:', error);
          return false;
        }
      },
      
      stopAutonomousMode: async () => {
        try {
          set((state) => ({
            autonomousSettings: {
              ...state.autonomousSettings,
              enabled: false,
            },
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive: false,
              currentActivity: undefined,
              pausedReason: 'Manually stopped',
            },
          }));
          
          get().addAutonomousActivity({
            type: 'paused',
            message: 'Autonomous mode stopped by user',
            success: true,
          });
          
          return true;
        } catch (error) {
          console.error('Failed to stop autonomous mode:', error);
          return false;
        }
      },
      
      pauseAutonomousMode: async (reason: string) => {
        try {
          set((state) => ({
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive: false,
              currentActivity: undefined,
              pausedReason: reason,
            },
          }));
          
          get().addAutonomousActivity({
            type: 'paused',
            message: `Autonomous mode paused: ${reason}`,
            success: true,
          });
          
          return true;
        } catch (error) {
          console.error('Failed to pause autonomous mode:', error);
          return false;
        }
      },
      
      resumeAutonomousMode: async () => {
        try {
          set((state) => ({
            autonomousStatus: {
              ...state.autonomousStatus,
              isActive: true,
              currentActivity: 'Resuming autonomous mode...',
              pausedReason: undefined,
            },
          }));
          
          get().addAutonomousActivity({
            type: 'resumed',
            message: 'Autonomous mode resumed',
            success: true,
          });
          
          return true;
        } catch (error) {
          console.error('Failed to resume autonomous mode:', error);
          return false;
        }
      },
      
      getAutonomousStatus: () => {
        return get().autonomousStatus;
      },
      
      addAutonomousActivity: (activity: Omit<AutonomousActivity, 'id' | 'timestamp'>) => {
        const newActivity: AutonomousActivity = {
          ...activity,
          id: `activity_${Date.now()}`,
          timestamp: new Date(),
        };
        
        set((state) => ({
          autonomousActivities: [newActivity, ...state.autonomousActivities].slice(0, 100), // Keep last 100 activities
        }));
      },
      
      clearAutonomousActivities: () => {
        set({ autonomousActivities: [] });
      },
      
      // Safety control methods
      checkDailyLimit: () => {
        const state = get();
        return state.autonomousStatus.applicationsToday >= state.autonomousSettings.maxApplicationsPerDay;
      },
      
      checkWeeklyLimit: () => {
        const state = get();
        return state.autonomousStatus.applicationsThisWeek >= state.autonomousSettings.maxApplicationsPerWeek;
      },
      
      checkBudgetOverride: (propertyPrice: number, userMaxBudget: number) => {
        const state = get();
        const maxAllowedPrice = userMaxBudget * (1 + state.autonomousSettings.maxBudgetOverride / 100);
        return propertyPrice <= maxAllowedPrice;
      },
      
      shouldRequireConfirmation: (propertyPrice: number, userMaxBudget: number) => {
        const state = get();
        if (!state.autonomousSettings.requireConfirmationForExpensive) return false;
        
        const significantlyOverBudget = propertyPrice > userMaxBudget * 1.15; // 15% over budget
        return significantlyOverBudget;
      },
      
      checkRecentRejections: () => {
        const state = get();
        if (!state.autonomousSettings.pauseOnMultipleRejections) return false;
        
        // Check for 3 or more rejections in the last 24 hours
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const recentRejections = state.applications.filter(app => {
          if (app.response?.status !== 'rejected' || !app.response.receivedAt) return false;
          const receivedDate = typeof app.response.receivedAt === 'string' 
            ? new Date(app.response.receivedAt) 
            : app.response.receivedAt;
          return receivedDate > oneDayAgo;
        });
        
        return recentRejections.length >= 3;
      },
      
      enforceApplicationLimits: async () => {
        const state = get();
        const actions = get();
        
        // Check daily limit
        if (actions.checkDailyLimit()) {
          await actions.pauseAutonomousMode('Daily application limit reached');
          actions.addAutonomousActivity({
            type: 'paused',
            message: `Autonomous mode paused: Daily limit of ${state.autonomousSettings.maxApplicationsPerDay} applications reached`,
            success: true,
          });
          return false;
        }
        
        // Check weekly limit
        if (actions.checkWeeklyLimit()) {
          await actions.pauseAutonomousMode('Weekly application limit reached');
          actions.addAutonomousActivity({
            type: 'paused',
            message: `Autonomous mode paused: Weekly limit of ${state.autonomousSettings.maxApplicationsPerWeek} applications reached`,
            success: true,
          });
          return false;
        }
        
        // Check recent rejections
        if (actions.checkRecentRejections()) {
          await actions.pauseAutonomousMode('Multiple rejections detected');
          actions.addAutonomousActivity({
            type: 'paused',
            message: 'Autonomous mode paused: Multiple rejections detected in the last 24 hours',
            success: true,
          });
          return false;
        }
        
        return true;
      },
      
      resetDailyCounters: () => {
        set((state) => ({
          autonomousStatus: {
            ...state.autonomousStatus,
            applicationsToday: 0,
          },
        }));
      },
      
      resetWeeklyCounters: () => {
        set((state) => ({
          autonomousStatus: {
            ...state.autonomousStatus,
            applicationsThisWeek: 0,
          },
        }));
      },
      
      // Translation actions
      translateText: async (text: string, targetLanguage: 'en' | 'nl') => {
        const cacheKey = `${text}_${targetLanguage}`;
        const state = get();
        
        // Check cache first
        if (state.translationCache.has(cacheKey)) {
          return state.translationCache.get(cacheKey)!;
        }
        
        set({ translationInProgress: true });
        
        try {
          const request: TranslationRequest = {
            text,
            targetLanguage,
          };
          
          const response = await aiService.translateText(request);
          
          if (response.success && response.data) {
            // Update cache
            const newCache = new Map(state.translationCache);
            newCache.set(cacheKey, response.data);
            
            set({
              translationCache: newCache,
              translationInProgress: false,
            });
            
            return response.data;
          } else {
            set({ translationInProgress: false });
            throw new Error(response.message || 'Translation failed');
          }
        } catch (error: any) {
          set({ translationInProgress: false });
          throw error;
        }
      },
      
      clearTranslationCache: () => {
        set({ translationCache: new Map() });
      },
      
      // Utility actions
      clearError: () => {
        set({
          error: null,
          matchingError: null,
          applicationError: null,
          analysisError: null,
          insightsError: null,
        });
      },
      
      clearAllData: () => {
        set({
          matches: [],
          applications: [],
          contractAnalyses: [],
          marketInsights: [],
          autonomousActivities: [],
          translationCache: new Map(),
          lastMatchUpdate: null,
          error: null,
          matchingError: null,
          applicationError: null,
          analysisError: null,
          insightsError: null,
        });
      },
      
      getMatchById: (matchId: string) => {
        return get().matches.find((match) => match.id === matchId);
      },
      
      getApplicationById: (applicationId: string) => {
        return get().applications.find((app) => app.id === applicationId);
      },
      
      getAnalysisById: (analysisId: string) => {
        return get().contractAnalyses.find((analysis) => analysis.id === analysisId);
      },
    }),
    {
      name: "ai-storage",
      storage: createJSONStorage(() => AsyncStorage),
      // Persist most data but exclude loading states and cache
      partialize: (state) => ({
        matches: state.matches,
        applications: state.applications,
        contractAnalyses: state.contractAnalyses,
        marketInsights: state.marketInsights,
        autonomousSettings: state.autonomousSettings,
        autonomousStatus: state.autonomousStatus,
        autonomousActivities: state.autonomousActivities,
        lastMatchUpdate: state.lastMatchUpdate,
      }),
    }
  )
);