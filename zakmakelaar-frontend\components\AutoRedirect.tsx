import React, { useEffect } from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuthStore } from '../store/authStore';
import { navigationService } from '../services/navigationService';

interface AutoRedirectProps {
  children: React.ReactNode;
  redirectIfAuthenticated?: boolean;
  redirectIfUnauthenticated?: boolean;
  redirectIfNoPreferences?: boolean;
  redirectTo?: string;
}

/**
 * Component that automatically redirects based on user authentication state
 */
export const AutoRedirect: React.FC<AutoRedirectProps> = ({
  children,
  redirectIfAuthenticated = false,
  redirectIfUnauthenticated = false,
  redirectIfNoPreferences = false,
  redirectTo
}) => {
  const router = useRouter();
  const { isAuthenticated, isLoading, user } = useAuthStore();
  
  useEffect(() => {
    const checkRedirection = async () => {
      // Skip redirection if still loading
      if (isLoading) return;
      
      // Redirect if authenticated and redirectIfAuthenticated is true
      if (isAuthenticated && redirectIfAuthenticated) {
        if (redirectTo) {
          router.replace(redirectTo);
        } else {
          await navigationService.navigateBasedOnAuthState();
        }
        return;
      }
      
      // Redirect if unauthenticated and redirectIfUnauthenticated is true
      if (!isAuthenticated && redirectIfUnauthenticated) {
        if (redirectTo) {
          router.replace(redirectTo);
        } else {
          router.replace('/');
        }
        return;
      }
      
      // Redirect if authenticated but no preferences and redirectIfNoPreferences is true
      if (isAuthenticated && redirectIfNoPreferences && (!user || !user.preferences)) {
        if (redirectTo) {
          router.replace(redirectTo);
        } else {
          router.replace('/preferences');
        }
        return;
      }
    };
    
    checkRedirection();
  }, [isAuthenticated, isLoading, user, redirectIfAuthenticated, redirectIfUnauthenticated, redirectIfNoPreferences, redirectTo]);
  
  // Show loading indicator while checking authentication
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f72585" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }
  
  // Render children if no redirection is needed
  return <>{children}</>;
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0a0a18',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#ffffff',
  }
});