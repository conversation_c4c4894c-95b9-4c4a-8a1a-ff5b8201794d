import axios, { AxiosInstance, AxiosResponse, AxiosError } from "axios";
import * as SecureStore from "expo-secure-store";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { getApiBaseUrl, API_CONFIG, DEFAULT_HEADERS } from "../config/api";

// API Configuration
const apiConfig = {
  baseURL: getApiBaseUrl(),
  timeout: API_CONFIG.TIMEOUT,
  headers: DEFAULT_HEADERS,
};

// Storage keys
const STORAGE_KEYS = {
  ACCESS_TOKEN: "access_token",
  REFRESH_TOKEN: "refresh_token",
  USER_DATA: "user_data",
};

// API Response types
export interface ApiResponse<T = any> {
  total: any;
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  status?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Error types
export interface ApiError {
  message: string;
  status: number;
  code?: string;
}

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create(apiConfig);
    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      async (config) => {
        try {
          const token = await SecureStore.getItemAsync(
            STORAGE_KEYS.ACCESS_TOKEN
          );
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        } catch (error) {
          console.warn("Failed to get auth token:", error);
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as any;
        
        // Handle rate limiting (429 Too Many Requests)
        if (error.response?.status === 429 && !originalRequest._retryCount) {
          // Initialize retry count
          originalRequest._retryCount = 1;
          
          const retryAfter = error.response.headers['retry-after'] 
            ? parseInt(error.response.headers['retry-after'], 10) * 1000 
            : 2000 * originalRequest._retryCount; // Default backoff starting at 2 seconds
          
          console.warn(`Rate limited. Retrying after ${retryAfter}ms`);
          
          // Wait for the specified time before retrying
          return new Promise(resolve => {
            setTimeout(() => {
              resolve(this.client(originalRequest));
            }, retryAfter);
          });
        }
        
        // Handle retry for rate limiting with exponential backoff
        if (error.response?.status === 429 && originalRequest._retryCount < 3) {
          originalRequest._retryCount++;
          
          // Exponential backoff: 2s, 4s, 8s
          const backoffTime = Math.pow(2, originalRequest._retryCount) * 1000;
          
          console.warn(`Rate limited. Retry attempt ${originalRequest._retryCount} after ${backoffTime}ms`);
          
          return new Promise(resolve => {
            setTimeout(() => {
              resolve(this.client(originalRequest));
            }, backoffTime);
          });
        }

        // Handle 401 errors (unauthorized)
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = await SecureStore.getItemAsync(
              STORAGE_KEYS.REFRESH_TOKEN
            );
            if (refreshToken) {
              // Try to refresh the token
              const response = await this.refreshToken(refreshToken);
              if (response.success && response.data?.token) {
                await SecureStore.setItemAsync(
                  STORAGE_KEYS.ACCESS_TOKEN,
                  response.data.token
                );
                originalRequest.headers.Authorization = `Bearer ${response.data.token}`;
                return this.client(originalRequest);
              }
            }
          } catch (refreshError) {
            console.error("Token refresh failed:", refreshError);
          }

          // If refresh fails, clear tokens and redirect to login
          await this.clearAuthData();
          // You might want to emit an event here to redirect to login
        }

        return Promise.reject(this.handleError(error));
      }
    );
  }

  private handleError(error: AxiosError): ApiError {
    if (error.response) {
      // Server responded with error status
      const data = error.response.data as any;
      
      // Special handling for rate limiting errors
      if (error.response.status === 429) {
        const retryAfter = error.response.headers['retry-after'];
        const waitTime = retryAfter ? parseInt(retryAfter, 10) : 30;
        
        return {
          message: `Too many requests. Please try again in ${waitTime} seconds.`,
          status: 429,
          code: "RATE_LIMITED",
        };
      }
      
      return {
        message: data?.message || data?.error || "An error occurred",
        status: error.response.status,
        code: data?.code,
      };
    } else if (error.request) {
      // Network error
      return {
        message: "Network error. Please check your connection.",
        status: 0,
        code: "NETWORK_ERROR",
      };
    } else {
      // Other error
      return {
        message: error.message || "An unexpected error occurred",
        status: 0,
        code: "UNKNOWN_ERROR",
      };
    }
  }

  // Generic HTTP methods
  async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.get(url, { params });

      // Transform backend response format to frontend format
      const backendResponse = response.data;

      if (backendResponse.status === "success") {
        // Handle different response structures
        let responseData =
          backendResponse.data || backendResponse.user || backendResponse;

        // For single listing endpoints, extract the listing from nested structure
        if (
          responseData &&
          responseData.listing &&
          url.includes("/listings/")
        ) {
          responseData = responseData.listing;
        }

        return {
          success: true,
          data: responseData,
          message: backendResponse.message,
        };
      } else {
        return {
          success: false,
          error: backendResponse.message || backendResponse.error,
          message: backendResponse.message || backendResponse.error,
        };
      }
    } catch (error) {
      throw error;
    }
  }

  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.post(url, data);

      // Transform backend response format to frontend format
      const backendResponse = response.data;

      if (backendResponse.status === "success") {
        // For auth endpoints, include token at data level
        const transformedData = backendResponse.data
          ? {
              ...backendResponse.data,
              token: backendResponse.token,
            }
          : {
              ...backendResponse,
              token: backendResponse.token,
            };

        return {
          success: true,
          data: transformedData,
          message: backendResponse.message,
        };
      } else {
        return {
          success: false,
          error: backendResponse.message || backendResponse.error,
          message: backendResponse.message || backendResponse.error,
        };
      }
    } catch (error) {
      throw error;
    }
  }

  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      // Log the request for debugging
      console.log(`API PUT Request to ${url}:`, JSON.stringify(data, null, 2));
      
      const response = await this.client.put(url, data);
      
      // Transform backend response format to frontend format
      const backendResponse = response.data;
      
      if (backendResponse.status === "success") {
        return {
          success: true,
          data: backendResponse.data || backendResponse.user || backendResponse,
          message: backendResponse.message,
        };
      } else {
        return {
          success: false,
          error: backendResponse.message || backendResponse.error,
          message: backendResponse.message || backendResponse.error,
        };
      }
    } catch (error: any) {
      // Enhanced error logging
      console.error(`API PUT Error to ${url}:`, error);
      
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
      }
      
      throw error;
    }
  }

  async delete<T>(url: string): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.delete(url);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Auth token management
  async saveAuthData(token: string, refreshToken?: string, userData?: any) {
    try {
      console.log('API Service - Saving auth data');
      
      // Ensure token is a string
      const tokenString = typeof token === "string" ? token : String(token);
      await SecureStore.setItemAsync(STORAGE_KEYS.ACCESS_TOKEN, tokenString);
      console.log('API Service - Auth token saved');

      if (refreshToken) {
        const refreshTokenString =
          typeof refreshToken === "string"
            ? refreshToken
            : String(refreshToken);
        await SecureStore.setItemAsync(
          STORAGE_KEYS.REFRESH_TOKEN,
          refreshTokenString
        );
        console.log('API Service - Refresh token saved');
      }

      if (userData) {
        // Ensure propertyOwner object exists if user is a property owner
        if (userData.role === 'owner' && !userData.propertyOwner) {
          console.log('API Service - Adding missing propertyOwner object for owner role');
          userData.propertyOwner = {
            isPropertyOwner: true,
            properties: [],
            verificationStatus: 'pending'
          };
        }
        
        await AsyncStorage.setItem(
          STORAGE_KEYS.USER_DATA,
          JSON.stringify(userData)
        );
        console.log('API Service - User data saved successfully');
      }
    } catch (error: any) {
      console.error("Failed to save auth data:", error?.message || error);
      throw error;
    }
  }

  async getAuthToken(): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(STORAGE_KEYS.ACCESS_TOKEN);
    } catch (error) {
      console.error("Failed to get auth token:", error);
      return null;
    }
  }

  async getUserData(): Promise<any | null> {
    try {
      console.log('API Service - Getting user data from storage');
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      
      if (!userData) {
        console.log('API Service - No user data found in storage');
        return null;
      }
      
      const parsedUserData = JSON.parse(userData);
      
      return parsedUserData;
    } catch (error) {
      console.error("Failed to get user data:", error);
      return null;
    }
  }

  async clearAuthData() {
    try {
      await SecureStore.deleteItemAsync(STORAGE_KEYS.ACCESS_TOKEN);
      await SecureStore.deleteItemAsync(STORAGE_KEYS.REFRESH_TOKEN);
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
    } catch (error) {
      console.error("Failed to clear auth data:", error);
    }
  }

  async isAuthenticated(): Promise<boolean> {
    try {
      const token = await SecureStore.getItemAsync(STORAGE_KEYS.ACCESS_TOKEN);
      return !!token;
    } catch (error) {
      return false;
    }
  }

  // Token refresh
  private async refreshToken(refreshToken: string): Promise<ApiResponse> {
    try {
      const response = await axios.post(`${apiConfig.baseURL}/auth/refresh`, {
        refreshToken,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    try {
      // Use the client instance with retry logic instead of direct axios call
      const response = await this.client.get(
        `/health`,
        { baseURL: apiConfig.baseURL.replace("/api", "") }
      );
      return response.data;
    } catch (error) {
      // Handle error but don't throw to prevent app initialization failure
      console.warn("Health check error:", error);
      return {
        success: false,
        message: "Health check failed, but app will continue to initialize"
      };
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
