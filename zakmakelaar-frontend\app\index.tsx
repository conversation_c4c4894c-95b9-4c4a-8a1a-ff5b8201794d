import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  Platform,
  Dimensions,
} from "react-native";
import { useRouter } from "expo-router";
import { Image } from "expo-image";
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { 
  FadeIn, 
  FadeInDown, 
  FadeInUp,
  SlideInRight,
  SlideInLeft,
  ZoomIn,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withDelay,
  withSequence,
  withRepeat,
  withSpring,
  Easing
} from 'react-native-reanimated';
import { WelcomeBackground } from "../components/WelcomeBackground";
import { AIFeatureHighlight } from "../components/AIFeatureHighlight";
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff'
};

export default function WelcomeScreen() {
  const router = useRouter();
  const [showExtraFeatures, setShowExtraFeatures] = useState(false);
  
  // Animation values
  const logoScale = useSharedValue(1);
  const logoRotate = useSharedValue(0);
  const buttonScale = useSharedValue(1);
  const buttonGlow = useSharedValue(0.5);
  const taglineOpacity = useSharedValue(0);
  const taglineTranslateY = useSharedValue(20);
  const heroTextScale = useSharedValue(0.95);
  
  // Animate logo effects
  useEffect(() => {
    // Logo pulse animation
    logoScale.value = withRepeat(
      withSequence(
        withTiming(1.08, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
        withTiming(1, { duration: 2000, easing: Easing.inOut(Easing.ease) })
      ),
      -1, // Infinite repeat
      true // Reverse
    );
    
    // Logo subtle rotation
    logoRotate.value = withRepeat(
      withSequence(
        withTiming(8, { duration: 4000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-8, { duration: 4000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    // Button animations
    buttonScale.value = withRepeat(
      withSequence(
        withTiming(1.05, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
        withTiming(1, { duration: 2000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    buttonGlow.value = withRepeat(
      withSequence(
        withTiming(0.8, { duration: 1500, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.5, { duration: 1500, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    // Tagline animations
    taglineOpacity.value = withTiming(1, { duration: 1000, easing: Easing.out(Easing.ease) });
    taglineTranslateY.value = withTiming(0, { duration: 1000, easing: Easing.out(Easing.ease) });
    
    // Hero text scale animation
    heroTextScale.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 3000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.98, { duration: 3000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    // Show extra features after a delay
    const timer = setTimeout(() => {
      setShowExtraFeatures(true);
    }, 2500);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Animated styles
  const logoAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: logoScale.value },
        { rotate: `${logoRotate.value}deg` }
      ]
    };
  });
  
  const buttonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: buttonScale.value }]
    };
  });
  
  const buttonGlowStyle = useAnimatedStyle(() => {
    return {
      opacity: buttonGlow.value
    };
  });
  
  const taglineAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: taglineOpacity.value,
      transform: [{ translateY: taglineTranslateY.value }]
    };
  });
  
  const heroTextAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: heroTextScale.value }]
    };
  });
  
  const handleGetStarted = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Mark welcome screen as seen
    try {
      const { welcomeService } = await import('../services/welcomeService');
      await welcomeService.markWelcomeSeen();
    } catch (error) {
      console.error('Error marking welcome as seen:', error);
    }
    
    // Use navigation service to track onboarding progress
    import('../services/navigationService').then(({ navigationService }) => {
      navigationService.navigateToLogin();
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Enhanced Animated Background */}
      <WelcomeBackground 
        primaryColor={THEME.primary} 
        secondaryColor={THEME.secondary}
        accentColor={THEME.accent}
      />
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Logo and Title Section */}
        <View style={styles.headerSection}>
          <View style={styles.logoWrapper}>
            {/* Glow effect behind logo */}
            <Animated.View 
              style={[styles.logoGlow, logoAnimatedStyle]}
              entering={FadeIn.duration(1200)}
            >
              <LinearGradient
                colors={[THEME.accent, THEME.primary]}
                style={styles.logoGlowGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              />
            </Animated.View>
            
            {/* Logo container with hexagon shape */}
            <Animated.View 
              style={[styles.logoContainer, logoAnimatedStyle]}
              entering={ZoomIn.duration(800).springify()}
            >
              <LinearGradient
                colors={[THEME.primary, THEME.secondary]}
                style={styles.logoGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Text style={styles.logoText}>ZM</Text>
              </LinearGradient>
            </Animated.View>
            
            {/* Tech decoration elements */}
            <Animated.View 
              style={[styles.techDecoration, { top: -10, right: -10 }]}
              entering={FadeIn.delay(1000).duration(800)}
            >
              <View style={styles.techCircle} />
              <View style={[styles.techLine, { width: 20 }]} />
            </Animated.View>
            
            <Animated.View 
              style={[styles.techDecoration, { bottom: -5, left: -15 }]}
              entering={FadeIn.delay(1200).duration(800)}
            >
              <View style={styles.techCircle} />
              <View style={[styles.techLine, { width: 15 }]} />
            </Animated.View>
          </View>
          
          <Animated.View 
            style={[styles.titleContainer, heroTextAnimatedStyle]}
            entering={FadeInDown.delay(300).duration(800)}
          >
            <LinearGradient
              colors={['#ffffff', '#e0e0ff']}
              style={styles.titleGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.title}>ZakMakelaar</Text>
            </LinearGradient>
          </Animated.View>
          
          <Animated.Text 
            style={styles.subtitle}
            entering={FadeInDown.delay(600).duration(800)}
          >
            Your AI-powered rental assistant
          </Animated.Text>
          
          {/* Tagline with animation */}
          <Animated.View 
            style={[styles.taglineContainer, taglineAnimatedStyle]}
          >
            <LinearGradient
              colors={[THEME.accent, THEME.primary]}
              style={styles.taglineBadge}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.taglineText}>Autonomous. Intelligent. Effortless.</Text>
            </LinearGradient>
          </Animated.View>
        </View>
        
        {/* Hero Message */}
        <Animated.View 
          style={styles.heroMessageContainer}
          entering={FadeInDown.delay(900).duration(800)}
        >
          <Text style={styles.heroMessage}>
            Let AI handle your entire rental journey - from finding perfect matches to automatically submitting applications
          </Text>
        </Animated.View>
        
        {/* AI Features Section */}
        <View style={styles.featuresSection}>
          <Animated.Text 
            style={styles.sectionTitle}
            entering={FadeInDown.delay(1100).duration(800)}
          >
            The future of renting is here
          </Animated.Text>
          
          <AIFeatureHighlight
            icon="search-outline"
            title="AI-Powered Matching"
            description="Our AI finds perfect properties based on your preferences and priorities, with personalized match scores."
            delay={1300}
            primaryColor={THEME.primary}
            secondaryColor={THEME.secondary}
            accentColor={THEME.accent}
          />
          
          <AIFeatureHighlight
            icon="document-text-outline"
            title="Autonomous Applications"
            description="Let our AI generate and submit personalized rental applications automatically when it finds your ideal home."
            delay={1500}
            primaryColor={THEME.secondary}
            secondaryColor={THEME.primary}
            accentColor={THEME.accent}
          />
          
          <AIFeatureHighlight
            icon="notifications-outline"
            title="Smart Notifications"
            description="Get real-time alerts about new matches, application status updates, and landlord responses."
            delay={1700}
            primaryColor={THEME.accent}
            secondaryColor={THEME.primary}
            accentColor={THEME.secondary}
          />
          
          {showExtraFeatures && (
            <>
              <AIFeatureHighlight
                icon="analytics-outline"
                title="Market Insights"
                description="Access AI-generated market analysis, pricing trends, and neighborhood comparisons to make informed decisions."
                delay={300}
                primaryColor={THEME.primary}
                secondaryColor={THEME.accent}
                accentColor={THEME.secondary}
              />
              
              <AIFeatureHighlight
                icon="shield-checkmark-outline"
                title="Contract Analysis"
                description="Our AI reviews rental contracts to highlight important clauses, potential issues, and recommendations."
                delay={500}
                primaryColor={THEME.secondary}
                secondaryColor={THEME.primary}
                accentColor={THEME.accent}
              />
            </>
          )}
        </View>
        
        {/* Stats Section */}
        <Animated.View 
          style={styles.statsSection}
          entering={FadeInUp.delay(1900).duration(800)}
        >
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>93%</Text>
            <Text style={styles.statLabel}>Match Accuracy</Text>
          </View>
          
          <View style={styles.statDivider} />
          
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>2x</Text>
            <Text style={styles.statLabel}>Faster Process</Text>
          </View>
          
          <View style={styles.statDivider} />
          
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>24/7</Text>
            <Text style={styles.statLabel}>Autonomous</Text>
          </View>
        </Animated.View>
        
        {/* Get Started Button with enhanced design */}
        <Animated.View 
          style={styles.buttonContainer}
          entering={FadeInUp.delay(2100).duration(800)}
        >
          {/* Button glow effect */}
          <Animated.View style={[styles.buttonGlow, buttonGlowStyle]}>
            <LinearGradient
              colors={[THEME.accent, THEME.primary]}
              style={styles.buttonGlowGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
          </Animated.View>
          
          {/* Actual button with gradient */}
          <Animated.View style={buttonAnimatedStyle}>
            <TouchableOpacity 
              style={styles.getStartedButton}
              onPress={handleGetStarted}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={[THEME.accent, THEME.secondary]}
                style={styles.buttonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.getStartedText}>Get Started</Text>
                <Ionicons name="arrow-forward" size={20} color="white" style={styles.buttonIcon} />
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>

        {/* Skip Welcome Option */}
        <Animated.View 
          style={styles.skipContainer}
          entering={FadeInUp.delay(2300).duration(800)}
        >
          <TouchableOpacity 
            style={styles.skipButton}
            onPress={handleGetStarted}
            activeOpacity={0.7}
          >
            <Text style={styles.skipText}>Skip intro next time</Text>
            <Ionicons name="checkmark-circle-outline" size={16} color="rgba(255, 255, 255, 0.6)" />
          </TouchableOpacity>
        </Animated.View>
        
        {/* Footer with version info */}
        <Animated.View 
          style={styles.footer}
          entering={FadeIn.delay(2300).duration(800)}
        >
          <Text style={styles.footerText}>Experience the future of rental search</Text>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.dark,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'android' ? 40 : 20,
    paddingBottom: 40,
  },
  headerSection: {
    alignItems: "center",
    marginBottom: 40,
  },
  logoWrapper: {
    position: 'relative',
    width: 140,
    height: 140,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  logoGlow: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 35,
    overflow: 'hidden',
  },
  logoGlowGradient: {
    width: '100%',
    height: '100%',
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  logoGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 48,
    fontWeight: "bold",
    color: THEME.light,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  techDecoration: {
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
  },
  techCircle: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: THEME.accent,
  },
  techLine: {
    height: 2,
    backgroundColor: THEME.accent,
    opacity: 0.7,
  },
  titleContainer: {
    marginBottom: 12,
    borderRadius: 8,
    overflow: 'hidden',
  },
  titleGradient: {
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  title: {
    fontSize: 36,
    fontWeight: "bold",
    color: THEME.dark,
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: "center",
    marginBottom: 16,
  },
  taglineContainer: {
    marginTop: 8,
    borderRadius: 20,
    overflow: 'hidden',
  },
  taglineBadge: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20,
  },
  taglineText: {
    fontSize: 14,
    fontWeight: "600",
    color: THEME.light,
    textAlign: "center",
  },
  heroMessageContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 40,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.12)',
  },
  heroMessage: {
    fontSize: 18,
    lineHeight: 26,
    color: THEME.light,
    textAlign: 'center',
    fontWeight: '500',
  },
  featuresSection: {
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: THEME.light,
    marginBottom: 24,
    textAlign: "center",
  },
  statsSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 40,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: THEME.accent,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginHorizontal: 10,
  },
  buttonContainer: {
    alignItems: "center",
    position: 'relative',
    marginBottom: 24,
  },
  buttonGlow: {
    position: 'absolute',
    width: width * 0.85,
    height: 64,
    borderRadius: 20,
    overflow: 'hidden',
  },
  buttonGlowGradient: {
    width: '100%',
    height: '100%',
  },
  getStartedButton: {
    width: width * 0.8,
    height: 60,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  buttonGradient: {
    width: '100%',
    height: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  getStartedText: {
    color: THEME.light,
    fontSize: 18,
    fontWeight: "600",
    marginRight: 8,
  },
  buttonIcon: {
    marginLeft: 4,
  },
  skipContainer: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  skipButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  skipText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    marginRight: 6,
  },
  footer: {
    alignItems: 'center',
    marginTop: 8,
  },
  footerText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.5)',
    textAlign: 'center',
  }
});