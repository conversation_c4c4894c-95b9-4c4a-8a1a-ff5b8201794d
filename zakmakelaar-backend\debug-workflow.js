const workflow = require('./src/services/applicationSubmissionWorkflow');

console.log('Workflow object:', workflow);
console.log('Workflow constructor:', workflow.constructor.name);
console.log('Available methods:', Object.getOwnPropertyNames(workflow));
console.log('Available methods (prototype):', Object.getOwnPropertyNames(Object.getPrototypeOf(workflow)));
console.log('getStatus method:', typeof workflow.getStatus);
console.log('submitApplication method:', typeof workflow.submitApplication);