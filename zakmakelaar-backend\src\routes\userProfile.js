const express = require('express');
const router = express.Router();
const userProfileService = require('../services/userProfileService');
const documentVaultService = require('../services/documentVaultService');
const { auth } = require('../middleware/auth');
const { loggers } = require('../services/logger');

/**
 * @swagger
 * components:
 *   schemas:
 *     AutoApplicationProfile:
 *       type: object
 *       properties:
 *         enabled:
 *           type: boolean
 *           description: Whether auto-application is enabled
 *         settings:
 *           type: object
 *           properties:
 *             maxApplicationsPerDay:
 *               type: integer
 *               minimum: 1
 *               maximum: 20
 *             applicationTemplate:
 *               type: string
 *               enum: [professional, casual, student, expat]
 *             autoSubmit:
 *               type: boolean
 *             requireManualReview:
 *               type: boolean
 *             notificationPreferences:
 *               type: object
 *               properties:
 *                 immediate:
 *                   type: boolean
 *                 daily:
 *                   type: boolean
 *                 weekly:
 *                   type: boolean
 *         criteria:
 *           type: object
 *           properties:
 *             maxPrice:
 *               type: number
 *             minRooms:
 *               type: integer
 *             maxRooms:
 *               type: integer
 *             propertyTypes:
 *               type: array
 *               items:
 *                 type: string
 *             locations:
 *               type: array
 *               items:
 *                 type: string
 *             excludeKeywords:
 *               type: array
 *               items:
 *                 type: string
 *             includeKeywords:
 *               type: array
 *               items:
 *                 type: string
 *         personalInfo:
 *           type: object
 *           properties:
 *             fullName:
 *               type: string
 *             email:
 *               type: string
 *               format: email
 *             phone:
 *               type: string
 *             dateOfBirth:
 *               type: string
 *               format: date
 *             nationality:
 *               type: string
 *             occupation:
 *               type: string
 *             employer:
 *               type: string
 *             monthlyIncome:
 *               type: number
 *               minimum: 0
 *             moveInDate:
 *               type: string
 *               format: date
 *             leaseDuration:
 *               type: integer
 *               minimum: 1
 *               maximum: 60
 *             numberOfOccupants:
 *               type: integer
 *               minimum: 1
 *               maximum: 10
 *             hasGuarantor:
 *               type: boolean
 *             guarantorInfo:
 *               type: object
 *               properties:
 *                 name:
 *                   type: string
 *                 email:
 *                   type: string
 *                 phone:
 *                   type: string
 *                 relationship:
 *                   type: string
 *                 monthlyIncome:
 *                   type: number
 *             emergencyContact:
 *               type: object
 *               properties:
 *                 name:
 *                   type: string
 *                 email:
 *                   type: string
 *                 phone:
 *                   type: string
 *                 relationship:
 *                   type: string
 *         requiredDocuments:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [income_proof, employment_contract, bank_statement, id_document, rental_reference]
 *               required:
 *                 type: boolean
 *               uploaded:
 *                 type: boolean
 *               documentId:
 *                 type: string
 *               lastChecked:
 *                 type: string
 *                 format: date-time
 *         profileCompleteness:
 *           type: object
 *           properties:
 *             personalInfo:
 *               type: integer
 *               minimum: 0
 *               maximum: 100
 *             documents:
 *               type: integer
 *               minimum: 0
 *               maximum: 100
 *             overall:
 *               type: integer
 *               minimum: 0
 *               maximum: 100
 *             lastCalculated:
 *               type: string
 *               format: date-time
 *         applicationHistory:
 *           type: object
 *           properties:
 *             totalApplications:
 *               type: integer
 *             successfulApplications:
 *               type: integer
 *             lastApplicationDate:
 *               type: string
 *               format: date-time
 *             dailyApplicationCount:
 *               type: integer
 *             dailyResetDate:
 *               type: string
 *               format: date-time
 *         isReady:
 *           type: boolean
 *         canApplyToday:
 *           type: boolean
 *         successRate:
 *           type: integer
 *     ProfileGuidance:
 *       type: object
 *       properties:
 *         nextSteps:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high]
 *         warnings:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               severity:
 *                 type: string
 *                 enum: [low, medium, high]
 *         recommendations:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               action:
 *                 type: string
 *         completeness:
 *           $ref: '#/components/schemas/AutoApplicationProfile/properties/profileCompleteness'
 */

/**
 * @swagger
 * /api/user-profile/auto-application:
 *   get:
 *     summary: Get auto-application profile
 *     tags: [User Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Auto-application profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationProfile'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/auto-application', auth, async (req, res) => {
  try {
    const profile = await userProfileService.getAutoApplicationProfile(req.user._id);
    
    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    loggers.app.error('Error getting auto-application profile:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/user-profile/auto-application/initialize:
 *   post:
 *     summary: Initialize auto-application profile
 *     tags: [User Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Auto-application profile initialized successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationProfile'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/auto-application/initialize', auth, async (req, res) => {
  try {
    const profile = await userProfileService.initializeAutoApplicationProfile(req.user._id);
    
    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    loggers.app.error('Error initializing auto-application profile:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/user-profile/auto-application/personal-info:
 *   put:
 *     summary: Update personal information for auto-application
 *     tags: [User Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AutoApplicationProfile/properties/personalInfo'
 *     responses:
 *       200:
 *         description: Personal information updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationProfile'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put('/auto-application/personal-info', auth, async (req, res) => {
  try {
    const profile = await userProfileService.updatePersonalInfo(req.user._id, req.body);
    
    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    loggers.app.error('Error updating personal info:', error);
    
    if (error.message.includes('Validation errors')) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
});

/**
 * @swagger
 * /api/user-profile/auto-application/settings:
 *   put:
 *     summary: Update auto-application settings
 *     tags: [User Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AutoApplicationProfile/properties/settings'
 *     responses:
 *       200:
 *         description: Settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationProfile'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put('/auto-application/settings', auth, async (req, res) => {
  try {
    const profile = await userProfileService.updateSettings(req.user._id, req.body);
    
    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    loggers.app.error('Error updating settings:', error);
    
    if (error.message.includes('Validation errors')) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
});

/**
 * @swagger
 * /api/user-profile/auto-application/criteria:
 *   put:
 *     summary: Update application criteria
 *     tags: [User Profile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AutoApplicationProfile/properties/criteria'
 *     responses:
 *       200:
 *         description: Criteria updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationProfile'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put('/auto-application/criteria', auth, async (req, res) => {
  try {
    const profile = await userProfileService.updateCriteria(req.user._id, req.body);
    
    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    loggers.app.error('Error updating criteria:', error);
    
    if (error.message.includes('Validation errors')) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
});

/**
 * @swagger
 * /api/user-profile/auto-application/documents:
 *   get:
 *     summary: Check document requirements
 *     tags: [User Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Document requirements retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     requiredDocuments:
 *                       $ref: '#/components/schemas/AutoApplicationProfile/properties/requiredDocuments'
 *                     missingDocuments:
 *                       type: array
 *                       items:
 *                         type: string
 *                     allRequiredUploaded:
 *                       type: boolean
 *                     documentCompleteness:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/auto-application/documents', auth, async (req, res) => {
  try {
    const documentStatus = await userProfileService.checkDocumentRequirements(req.user._id);
    
    res.json({
      success: true,
      data: documentStatus
    });
  } catch (error) {
    loggers.app.error('Error checking document requirements:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/user-profile/auto-application/guidance:
 *   get:
 *     summary: Get profile guidance and recommendations
 *     tags: [User Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile guidance retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/ProfileGuidance'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/auto-application/guidance', auth, async (req, res) => {
  try {
    const guidance = await userProfileService.getProfileGuidance(req.user._id);
    
    res.json({
      success: true,
      data: guidance
    });
  } catch (error) {
    loggers.app.error('Error getting profile guidance:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/user-profile/auto-application/enable:
 *   post:
 *     summary: Enable auto-application
 *     tags: [User Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Auto-application enabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationProfile'
 *       400:
 *         description: Profile not ready for auto-application
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/auto-application/enable', auth, async (req, res) => {
  try {
    const profile = await userProfileService.setAutoApplicationEnabled(req.user._id, true);
    
    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    loggers.app.error('Error enabling auto-application:', error);
    
    if (error.message.includes('Profile not ready')) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
});

/**
 * @swagger
 * /api/user-profile/auto-application/disable:
 *   post:
 *     summary: Disable auto-application
 *     tags: [User Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Auto-application disabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationProfile'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/auto-application/disable', auth, async (req, res) => {
  try {
    const profile = await userProfileService.setAutoApplicationEnabled(req.user._id, false);
    
    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    loggers.app.error('Error disabling auto-application:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/user-profile/auto-application/completeness:
 *   get:
 *     summary: Calculate and get profile completeness
 *     tags: [User Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile completeness calculated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/AutoApplicationProfile/properties/profileCompleteness'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/auto-application/completeness', auth, async (req, res) => {
  try {
    const completeness = await userProfileService.calculateProfileCompleteness(req.user._id);
    
    res.json({
      success: true,
      data: completeness
    });
  } catch (error) {
    loggers.app.error('Error calculating profile completeness:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;