console.log('Starting debug...');

// Test if the file can be loaded step by step
try {
  console.log('1. Testing file syntax...');
  const fs = require('fs');
  const content = fs.readFileSync('./src/services/applicationSubmissionWorkflow.js', 'utf8');
  console.log('File length:', content.length);
  console.log('File starts with:', content.substring(0, 100));
  console.log('File ends with:', content.substring(content.length - 100));
  
  console.log('2. Testing eval...');
  // Try to evaluate the file content
  const vm = require('vm');
  const context = {
    require: require,
    module: { exports: {} },
    exports: {},
    console: console,
    __dirname: __dirname,
    __filename: __filename
  };
  
  vm.createContext(context);
  vm.runInContext(content, context);
  
  console.log('3. Module exports:', context.module.exports);
  console.log('4. Exports type:', typeof context.module.exports);
  console.log('5. Exports constructor:', context.module.exports.constructor.name);
  
} catch (error) {
  console.error('Error:', error);
}