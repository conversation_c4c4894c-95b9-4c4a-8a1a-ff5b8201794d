import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { useAuthStore } from '../store/authStore';
import { apiService } from '../services/api';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
  Easing
} from 'react-native-reanimated';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff'
};

interface AppInitializerProps {
  children: React.ReactNode;
}

export default function AppInitializer({ children }: AppInitializerProps) {
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);
  const { checkAuthStatus } = useAuthStore();

  // Animation values
  const logoScale = useSharedValue(1);
  const logoRotate = useSharedValue(0);

  // Start animations
  useEffect(() => {
    // Logo pulse animation
    logoScale.value = withRepeat(
      withSequence(
        withTiming(1.08, { duration: 1500, easing: Easing.inOut(Easing.ease) }),
        withTiming(1, { duration: 1500, easing: Easing.inOut(Easing.ease) })
      ),
      -1, // Infinite repeat
      true // Reverse
    );

    // Logo subtle rotation
    logoRotate.value = withRepeat(
      withSequence(
        withTiming(8, { duration: 3000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-8, { duration: 3000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
  }, []);

  // Animated styles
  const logoAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: logoScale.value },
        { rotate: `${logoRotate.value}deg` }
      ]
    };
  });

  // Initialize app
  useEffect(() => {
   // console.log('AppInitializer mounted');

    // Add a small delay to show the splash screen
    const timer = setTimeout(async () => {
      try {
        // console.log('Starting app initialization...');

        // Check API health
        try {
          // console.log('Checking API health...');
          await apiService.healthCheck();
          // console.log('API health check passed');
        } catch (error) {
          console.warn('API health check failed:', error);
          // Don't fail initialization if health check fails
          // The app can still work offline or with cached data
        }

        // Check authentication status
       // console.log('Checking authentication status...');
        await checkAuthStatus();
       // console.log('Authentication status checked');

        // Complete initialization
       // console.log('App initialization complete');
        setIsInitializing(false);
      } catch (error: any) {
        console.error('App initialization failed:', error);
        setInitError(error.message || 'Failed to initialize app');
        setIsInitializing(false);
      }
    }, 1500);

    return () => {
      // console.log('Clearing initialization timer');
      clearTimeout(timer);
    };
  }, [checkAuthStatus]);

  if (isInitializing) {
    return (
      <View style={styles.container}>
        <Animated.View
          style={[styles.logoWrapper, logoAnimatedStyle]}
        >
          {/* Glow effect behind logo */}
          <View style={styles.logoGlow}>
            <LinearGradient
              colors={[THEME.accent, THEME.primary]}
              style={styles.logoGlowGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
          </View>

          {/* Logo container with gradient */}
          <View style={styles.logoContainer}>
            <LinearGradient
              colors={[THEME.primary, THEME.secondary]}
              style={styles.logoGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.logoText}>ZM</Text>
            </LinearGradient>
          </View>
        </Animated.View>

        <Animated.Text
          style={styles.appName}
          entering={FadeIn.delay(300).duration(600)}
        >
          ZakMakelaar
        </Animated.Text>

        <ActivityIndicator
          size="large"
          color={THEME.accent}
          style={styles.loader}
        />

        <Animated.Text
          style={styles.loadingText}
          entering={FadeIn.delay(600).duration(600)}
        >
          Initializing your AI rental assistant...
        </Animated.Text>
      </View>
    );
  }

  if (initError) {
    return (
      <View style={styles.container}>
        <View style={styles.logoWrapper}>
          <View style={styles.logoGlow}>
            <LinearGradient
              colors={['#ef4444', '#f97316']}
              style={styles.logoGlowGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
          </View>

          <View style={styles.logoContainer}>
            <LinearGradient
              colors={['#ef4444', '#b91c1c']}
              style={styles.logoGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.logoText}>ZM</Text>
            </LinearGradient>
          </View>
        </View>

        <Text style={styles.appName}>ZakMakelaar</Text>

        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Failed to initialize app: {initError}
          </Text>
          <Text style={styles.retryText}>
            Please check your internet connection and restart the app.
          </Text>
        </View>
      </View>
    );
  }

  return <>{children}</>;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: THEME.dark,
    padding: 20,
  },
  logoWrapper: {
    position: 'relative',
    width: 120,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  logoGlow: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 30,
    overflow: 'hidden',
  },
  logoGlowGradient: {
    width: '100%',
    height: '100%',
  },
  logoContainer: {
    width: '85%',
    height: '85%',
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  logoGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 40,
    fontWeight: "bold",
    color: THEME.light,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: THEME.light,
    marginBottom: 32,
    textAlign: 'center',
  },
  loader: {
    marginBottom: 24,
  },
  loadingText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginTop: 24,
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
    maxWidth: '90%',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 24,
  },
  retryText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 20,
  },
});